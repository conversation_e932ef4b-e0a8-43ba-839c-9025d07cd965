{"version": 3, "file": "prefer-immediate-return.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-immediate-return.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,0CAKwB;AACxB,gDAAwC;AAExC,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,iBAAiB,EACf,0GAA0G;SAC7G;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,gFAAgF;YAC7F,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,OAAO,EAAE,MAAM;KAChB;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,cAAc,CAAC,IAAmB;gBAChC,iBAAiB,CAAE,IAAgC,CAAC,IAAI,CAAC,CAAC;YAC5D,CAAC;YACD,UAAU,CAAC,IAAmB;gBAC5B,iBAAiB,CAAE,IAA4B,CAAC,UAAU,CAAC,CAAC;YAC9D,CAAC;SACF,CAAC;QAEF,SAAS,iBAAiB,CAAC,UAAgC;YACzD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,MAAM,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC/C,MAAM,kBAAkB,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBAEzD,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrD,MAAM,kBAAkB,GAAG,uBAAuB,CAAC,UAAU,CAAC,CAAC;gBAE/D,IAAI,kBAAkB,IAAI,kBAAkB,EAAE;oBAC5C,MAAM,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;wBACzD,OAAO,CACL,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,kBAAkB,CAAC;4BACpE,SAAS;4BACX,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,kBAAkB,CAAC,EAAE,CAAC;gCACvE,SAAS,CACZ,CAAC;oBACJ,CAAC,CAAC,CAAC;oBAEH,yDAAyD;oBACzD,IAAI,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;wBACpF,OAAO,CAAC,MAAM,CAAC;4BACb,SAAS,EAAE,mBAAmB;4BAC9B,IAAI,EAAE;gCACJ,MAAM,EAAE,IAAA,yBAAiB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;gCACpD,QAAQ,EAAE,kBAAkB,CAAC,IAAI;6BAClC;4BACD,IAAI,EAAE,kBAAkB,CAAC,IAAI;4BAC7B,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,kBAAkB,CAAC,IAAI,EAAE,kBAAkB,CAAC;yBAC5E,CAAC,CAAC;qBACJ;iBACF;aACF;QACH,CAAC;QAED,sCAAsC;QACtC,SAAS,GAAG,CACV,KAAyB,EACzB,IAAwB,EACxB,UAA8B,EAC9B,kBAAuC,EACvC,kBAAuC;YAEvC,MAAM,cAAc,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC3E,MAAM,kBAAkB,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,yBAAyB,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACvF,MAAM,gBAAgB,GACpB,yBAAyB,CAAC,MAAM,GAAG,CAAC;gBAClC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBACvC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpB,OAAO;gBACL,KAAK,CAAC,WAAW,CAAC,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;gBACzD,KAAK,CAAC,WAAW,CAAC,kBAAkB,EAAE,cAAc,CAAC;aACtD,CAAC;QACJ,CAAC;QAED,SAAS,uBAAuB,CAAC,IAAwB;YACvD,OAAO,CAAC,IAAA,yBAAiB,EAAC,IAAI,CAAC,IAAI,IAAA,wBAAgB,EAAC,IAAI,CAAC,CAAC;gBACxD,IAAI,CAAC,QAAQ;gBACb,IAAA,oBAAY,EAAC,IAAI,CAAC,QAAQ,CAAC;gBAC3B,CAAC,CAAC,IAAI,CAAC,QAAQ;gBACf,CAAC,CAAC,SAAS,CAAC;QAChB,CAAC;QAED,SAAS,uBAAuB,CAAC,IAAwB;YACvD,IAAI,IAAA,6BAAqB,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;gBACjE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC1C,IAAI,IAAA,oBAAY,EAAC,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE;oBAClD,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;iBACrB;aACF;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,YAAY,CAAC,OAA+C;YACnE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,qBAAqB,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC/E,IAAI,aAAa,KAAK,OAAO,CAAC,QAAQ,EAAE,EAAE;gBACxC,OAAO,qBAAqB,CAAC;aAC9B;iBAAM;gBACL,OAAO,qBAAqB,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;aAC9D;QACH,CAAC;IACH,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}