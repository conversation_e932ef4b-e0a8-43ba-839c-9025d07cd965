declare global {
    interface Window {
        electronAPI: {
            getAppVersion: () => Promise<string>;
            minimizeWindow: () => Promise<void>;
            maximizeWindow: () => Promise<void>;
            closeWindow: () => Promise<void>;
            database: {
                getUsers: () => Promise<any[]>;
                createUser: (userData: any) => Promise<any>;
                getProducts: (params?: any) => Promise<any>;
                getProduct: (id: number) => Promise<any>;
                createProduct: (productData: any) => Promise<any>;
                updateProduct: (id: number, updates: any) => Promise<any>;
                deleteProduct: (id: number) => Promise<void>;
                getCategories: () => Promise<any[]>;
                getCustomers: (params?: any) => Promise<any>;
                createCustomer: (customerData: any) => Promise<any>;
                getOrders: (params?: any) => Promise<any>;
                createOrder: (orderData: any) => Promise<any>;
                getArtists: () => Promise<any[]>;
                getInventory: (params?: any) => Promise<any>;
                updateInventory: (productId: number, quantity: number, type: string) => Promise<any>;
            };
            auth: {
                login: (username: string, password: string) => Promise<any>;
                logout: () => Promise<void>;
                getCurrentUser: () => Promise<any>;
            };
            files: {
                selectImage: () => Promise<string>;
                saveImage: (imageData: any, filename: string) => Promise<string>;
            };
            notifications: {
                show: (title: string, body: string) => Promise<void>;
            };
            on: (channel: string, callback: Function) => void;
            removeAllListeners: (channel: string) => void;
        };
    }
}
export {};
//# sourceMappingURL=preload.d.ts.map