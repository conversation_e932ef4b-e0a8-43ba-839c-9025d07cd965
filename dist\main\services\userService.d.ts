import { User, QueryResult, PaginatedResult, SearchFilters, SortOptions } from '../../shared/types/database';
/**
 * User service for authentication and user management
 */
export declare class UserService {
    /**
     * Create a new user
     */
    createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'passwordHash' | 'salt'> & {
        password: string;
    }): Promise<QueryResult<User>>;
    /**
     * Get user by ID
     */
    getUserById(id: number): Promise<QueryResult<User>>;
    /**
     * Get user by username
     */
    getUserByUsername(username: string): Promise<QueryResult<User>>;
    /**
     * Get user by email
     */
    getUserByEmail(email: string): Promise<QueryResult<User>>;
    /**
     * Get all users with pagination and filtering
     */
    getUsers(filters?: SearchFilters, sort?: SortOptions, page?: number, limit?: number): Promise<QueryResult<PaginatedResult<User>>>;
    /**
     * Update user
     */
    updateUser(id: number, userData: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'passwordHash' | 'salt'>>): Promise<QueryResult<User>>;
    /**
     * Update user password
     */
    updatePassword(id: number, newPassword: string): Promise<QueryResult<boolean>>;
    /**
     * Delete user (soft delete by setting inactive)
     */
    deleteUser(id: number): Promise<QueryResult<boolean>>;
    /**
     * Authenticate user (alias for authenticate method)
     */
    authenticateUser(username: string, password: string): Promise<QueryResult<User | null>>;
    /**
     * Authenticate user
     */
    authenticate(username: string, password: string): Promise<QueryResult<User | null>>;
    /**
     * Hash password with salt
     */
    private hashPassword;
    /**
     * Create default admin user if no users exist
     */
    createDefaultAdmin(): Promise<QueryResult<User | null>>;
}
export declare const userService: UserService;
//# sourceMappingURL=userService.d.ts.map