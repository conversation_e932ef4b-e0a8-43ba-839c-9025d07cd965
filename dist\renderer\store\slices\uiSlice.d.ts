export type Theme = 'light' | 'dark' | 'auto';
export type Language = 'en' | 'hi' | 'mai';
interface Notification {
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    duration?: number;
    timestamp: number;
}
interface UIState {
    theme: Theme;
    language: Language;
    sidebarCollapsed: boolean;
    loading: {
        global: boolean;
        products: boolean;
        customers: boolean;
        orders: boolean;
        inventory: boolean;
    };
    notifications: Notification[];
    modals: {
        productForm: boolean;
        customerForm: boolean;
        orderForm: boolean;
        confirmDialog: boolean;
    };
    currentPage: string;
    searchQuery: string;
    filters: {
        products: {
            category: string;
            priceRange: [number, number];
            inStock: boolean;
        };
        customers: {
            type: string;
            location: string;
        };
        orders: {
            status: string;
            dateRange: [string, string];
        };
    };
}
export declare const setTheme: import("@reduxjs/toolkit").ActionCreatorWithPayload<Theme, "ui/setTheme">, setLanguage: import("@reduxjs/toolkit").ActionCreatorWithPayload<Language, "ui/setLanguage">, toggleSidebar: import("@reduxjs/toolkit").ActionCreatorWithoutPayload<"ui/toggleSidebar">, setSidebarCollapsed: import("@reduxjs/toolkit").ActionCreatorWithPayload<boolean, "ui/setSidebarCollapsed">, setLoading: import("@reduxjs/toolkit").ActionCreatorWithPayload<{
    key: keyof UIState["loading"];
    value: boolean;
}, "ui/setLoading">, addNotification: import("@reduxjs/toolkit").ActionCreatorWithPayload<Omit<Notification, "id" | "timestamp">, "ui/addNotification">, removeNotification: import("@reduxjs/toolkit").ActionCreatorWithPayload<string, "ui/removeNotification">, clearNotifications: import("@reduxjs/toolkit").ActionCreatorWithoutPayload<"ui/clearNotifications">, openModal: import("@reduxjs/toolkit").ActionCreatorWithPayload<"productForm" | "customerForm" | "orderForm" | "confirmDialog", "ui/openModal">, closeModal: import("@reduxjs/toolkit").ActionCreatorWithPayload<"productForm" | "customerForm" | "orderForm" | "confirmDialog", "ui/closeModal">, closeAllModals: import("@reduxjs/toolkit").ActionCreatorWithoutPayload<"ui/closeAllModals">, setCurrentPage: import("@reduxjs/toolkit").ActionCreatorWithPayload<string, "ui/setCurrentPage">, setSearchQuery: import("@reduxjs/toolkit").ActionCreatorWithPayload<string, "ui/setSearchQuery">, setProductFilters: import("@reduxjs/toolkit").ActionCreatorWithPayload<Partial<{
    category: string;
    priceRange: [number, number];
    inStock: boolean;
}>, "ui/setProductFilters">, setCustomerFilters: import("@reduxjs/toolkit").ActionCreatorWithPayload<Partial<{
    type: string;
    location: string;
}>, "ui/setCustomerFilters">, setOrderFilters: import("@reduxjs/toolkit").ActionCreatorWithPayload<Partial<{
    status: string;
    dateRange: [string, string];
}>, "ui/setOrderFilters">, resetFilters: import("@reduxjs/toolkit").ActionCreatorWithoutPayload<"ui/resetFilters">;
declare const _default: import("redux").Reducer<UIState>;
export default _default;
//# sourceMappingURL=uiSlice.d.ts.map