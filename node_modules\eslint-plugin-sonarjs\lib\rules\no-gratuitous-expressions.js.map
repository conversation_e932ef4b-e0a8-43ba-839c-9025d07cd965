{"version": 3, "file": "no-gratuitous-expressions.js", "sourceRoot": "", "sources": ["../../src/rules/no-gratuitous-expressions.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,kDAA4C;AAC5C,0CAA6D;AAC7D,gDAAwC;AAExC,MAAM,OAAO,GAAG,qEAAqE,CAAC;AAEtF,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,yBAAyB,EAAE,OAAO;YAClC,YAAY,EAAE,sBAAsB;SACrC;QACD,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,8CAA8C;YAC3D,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,MAAM,EAAE;YACN;gBACE,0DAA0D;gBAC1D,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,SAAS,GAAwD,IAAI,GAAG,EAAE,CAAC;QACjF,MAAM,QAAQ,GAAwD,IAAI,GAAG,EAAE,CAAC;QAEhF,SAAS,WAAW;YAClB,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;YACzC,OAAO,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,wBAAwB,CAAC,CAAC;QAClF,CAAC;QAED,OAAO;YACL,WAAW,EAAE,CAAC,IAAmB,EAAE,EAAE;gBACnC,MAAM,EAAE,IAAI,EAAE,GAAG,IAA4B,CAAC;gBAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;oBAC9D,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;iBACnD;YACH,CAAC;YAED,YAAY,EAAE,CAAC,IAAmB,EAAE,EAAE;gBACpC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;gBACxB,IAAI,IAAA,qBAAa,EAAC,MAAM,CAAC,EAAE;oBACzB,6GAA6G;oBAC7G,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;oBAExC,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,EAAE;wBAC9B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBAC/D,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,kBAAkB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;wBAC3E,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,kBAAkB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;qBAC1E;yBAAM,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,IAAI,IAAA,oBAAY,EAAC,MAAM,CAAC,IAAI,CAAC,EAAE;wBACjE,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;qBACjF;iBACF;YACH,CAAC;YAED,iBAAiB,EAAE,CAAC,IAAmB,EAAE,EAAE;gBACzC,MAAM,IAAI,GAAG,IAA0B,CAAC;gBACxC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACvB,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;YAED,UAAU,EAAE,CAAC,IAAmB,EAAE,EAAE;gBAClC,MAAM,EAAE,GAAG,IAA2B,CAAC;gBACvC,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACjD,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,IAAI,eAAe,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;oBACxE,OAAO;iBACR;gBACD,IACE,CAAC,YAAY,CAAC,MAAM,CAAC;oBACrB,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC;oBAC3B,CAAC,IAAA,qBAAa,EAAC,MAAM,CAAC;oBACtB,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAC1B;oBACA,OAAO;iBACR;gBAED,MAAM,qBAAqB,GAAG,CAC5B,GAAwD,EACxD,MAAe,EACf,EAAE;oBACF,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;wBACvB,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;wBAC5D,IAAI,GAAG,EAAE;4BACP,WAAW,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;yBACvC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC;gBAEF,qBAAqB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBACvC,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YACzC,CAAC;YAED,OAAO,EAAE,GAAG,EAAE;gBACZ,SAAS,CAAC,KAAK,EAAE,CAAC;gBAClB,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,uBAAuB,CAAC,UAA+B;IAC9D,MAAM,MAAM,GAA0B,EAAE,CAAC;IACzC,MAAM,KAAK,GAA0B,EAAE,CAAC;IAExC,MAAM,SAAS,GAAG,CAAC,IAAyB,EAAE,EAAE;QAC9C,IAAI,IAAA,oBAAY,EAAC,IAAI,CAAC,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACnB;aAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;YAClC,IAAI,IAAA,oBAAY,EAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC/B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC3B;iBAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAA,oBAAY,EAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACnF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;aACrC;SACF;IACH,CAAC,CAAC;IAEF,IAAI,OAAO,GAAG,UAAU,CAAC;IACzB,SAAS,CAAC,OAAO,CAAC,CAAC;IACnB,OAAO,YAAY,CAAC,OAAO,CAAC,EAAE;QAC5B,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;KACxB;IACD,SAAS,CAAC,OAAO,CAAC,CAAC;IAEnB,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC3B,CAAC;AAED,SAAS,YAAY,CAAC,UAAyB;IAC7C,OAAO,UAAU,CAAC,IAAI,KAAK,mBAAmB,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC;AACjF,CAAC;AAED,SAAS,cAAc,CACrB,EAAuB,EACvB,UAAyB;IAEzB,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,mBAAmB;QACvC,UAAU,CAAC,QAAQ,KAAK,IAAI;QAC5B,UAAU,CAAC,IAAI,KAAK,EAAE,CACvB,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CACtB,EAAuB,EACvB,UAAyB;IAEzB,OAAO,CACL,UAAU,CAAC,MAAM,EAAE,IAAI,KAAK,mBAAmB;QAC/C,UAAU,CAAC,IAAI,KAAK,mBAAmB;QACvC,UAAU,CAAC,QAAQ,KAAK,IAAI;QAC5B,UAAU,CAAC,KAAK,KAAK,EAAE,CACxB,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAyB;IAClD,OAAO,UAAU,CAAC,IAAI,KAAK,iBAAiB,IAAI,UAAU,CAAC,QAAQ,KAAK,GAAG,CAAC;AAC9E,CAAC;AAED,SAAS,SAAS,CAAI,CAAuB;IAC3C,OAAO,CAAC,IAAI,IAAI,CAAC;AACnB,CAAC;AAED,SAAS,SAAS,CAAC,EAAuB,EAAE,KAA2B;IACrE,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC;IAC5D,IAAI,GAAG,EAAE;QACP,OAAO,GAAG,CAAC,QAAQ,CAAC;KACrB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,gBAAgB,CAAC,KAA2B;IACnD,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;QAC7B,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;QACvB,OAAO,IAAI,CAAC;KACb;IACD,OAAO,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,cAAc,CAAC,MAA+B,EAAE,YAAkC;IACzF,OAAO,MAAM,CAAC,UAAU;SACrB,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;SAC5B,IAAI,CAAC,GAAG,CAAC,EAAE;QACV,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC;QAE1B,IAAI,GAAG,GAAgC,QAAQ,CAAC;QAChD,OAAO,GAAG,EAAE;YACV,IAAI,GAAG,KAAK,YAAY,EAAE;gBACxB,OAAO,IAAI,CAAC;aACb;YACD,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC;SACjB;QAED,MAAM,WAAW,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAC3C,OAAO,OAAO,KAAK,WAAW,CAAC;IACjC,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,kBAAkB,CAAC,GAA0B,EAAE,YAAkC;IACxF,OAAO,GAAG;SACP,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC;SACxE,MAAM,CAAC,SAAS,CAAC;SACjB,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACtC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,QAAS,EAAE,YAAY,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,WAAW,CAClB,EAAiB,EACjB,GAAyC,EACzC,OAA+C,EAC/C,MAAe;IAEf,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC;IAC1C,IAAA,kBAAM,EACJ,OAAO,EACP;QACE,SAAS,EAAE,2BAA2B;QACtC,IAAI,EAAE;YACJ,KAAK;SACN;QACD,IAAI,EAAE,EAAE;KACT,EACD,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,EACjC,OAAO,CACR,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAAC,GAAyC,EAAE,MAAc;IACtF,IAAI,GAAG,EAAE;QACP,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,GAAI,CAAC;QACnC,OAAO;YACL;gBACE,OAAO,EAAE,wBAAwB,MAAM,EAAE;gBACzC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;gBACvB,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM;gBAC3B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI;gBACxB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;aAC7B;SACF,CAAC;KACH;SAAM;QACL,OAAO,EAAE,CAAC;KACX;AACH,CAAC;AAED,iBAAS,IAAI,CAAC"}