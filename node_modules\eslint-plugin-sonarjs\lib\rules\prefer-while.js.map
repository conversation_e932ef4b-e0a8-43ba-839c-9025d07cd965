{"version": 3, "file": "prefer-while.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-while.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,gDAAwC;AAExC,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,uBAAuB,EAAE,8CAA8C;SACxE;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,uDAAuD;YACpE,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,OAAO,EAAE,MAAM;KAChB;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,YAAY,CAAC,IAAmB;gBAC9B,MAAM,OAAO,GAAG,IAA6B,CAAC;gBAC9C,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC/D,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,UAAU,EAAE;oBAClE,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,yBAAyB;wBACpC,GAAG,EAAE,UAAU,CAAC,GAAG;wBACnB,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC;qBACrB,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;QAEF,SAAS,MAAM,CAAC,OAA8B;YAC5C,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;YACnC,MAAM,uBAAuB,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrF,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;YAE/B,IAAI,SAAS,IAAI,YAAY,IAAI,uBAAuB,EAAE;gBACxD,OAAO,CAAC,KAAyB,EAAE,EAAE;oBACnC,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM,GAAG,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC7C,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBACjE,OAAO,KAAK,CAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,UAAU,aAAa,GAAG,CAAC,CAAC;gBAC1E,CAAC,CAAC;aACH;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}