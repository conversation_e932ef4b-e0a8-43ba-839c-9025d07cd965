import React, { useState, useCallback } from 'react';
import {
  Card,
  CardHeader,
  CardPreview,
  Text,
  Button,
  Avatar,
  Badge,
  Divider,
  Field,
  Input,
  Textarea,
  MessageBar,
  MessageBarBody,
  makeStyles,
  tokens,
  shorthands,
} from '@fluentui/react-components';
import {
  PersonRegular,
  EditRegular,
  SaveRegular,
  DismissRegular,
  ShieldCheckmarkRegular,
  CalendarRegular,
  MailRegular,
  PhoneRegular,
} from '@fluentui/react-icons';
import { useAppSelector } from '../../store/hooks';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: '24px',
    maxWidth: '800px',
    margin: '0 auto',
    ...shorthands.padding('24px'),
  },
  profileCard: {
    ...shorthands.padding('24px'),
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    gap: '16px',
    marginBottom: '24px',
  },
  avatar: {
    width: '80px',
    height: '80px',
  },
  userInfo: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
  },
  userName: {
    fontSize: '24px',
    fontWeight: '600',
  },
  userRole: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  section: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
  },
  sectionTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: tokens.colorBrandForeground1,
  },
  infoGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
    gap: '16px',
  },
  infoItem: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    ...shorthands.padding('12px'),
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.borderRadius('8px'),
  },
  infoIcon: {
    color: tokens.colorBrandForeground1,
  },
  infoContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
  },
  infoLabel: {
    fontSize: '12px',
    color: tokens.colorNeutralForeground2,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  infoValue: {
    fontSize: '14px',
    color: tokens.colorNeutralForeground1,
  },
  editForm: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
  },
  formActions: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'flex-end',
    marginTop: '16px',
  },
  permissionsList: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '8px',
  },
  permissionBadge: {
    fontSize: '12px',
  },
});

const UserProfile: React.FC = React.memo(() => {
  const styles = useStyles();
  const { user, permissions, lastLoginAt } = useAppSelector((state) => state.auth);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    fullName: user?.fullName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    bio: user?.bio || '',
  });
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleEdit = useCallback(() => {
    setIsEditing(true);
    setEditData({
      fullName: user?.fullName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      bio: user?.bio || '',
    });
  }, [user]);

  const handleCancel = useCallback(() => {
    setIsEditing(false);
    setMessage(null);
  }, []);

  const handleSave = useCallback(async () => {
    setIsSaving(true);
    setMessage(null);

    try {
      // TODO: Implement user profile update API call
      // await window.electronAPI.database.updateUser(user.id, editData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMessage({ type: 'success', text: 'Profile updated successfully!' });
      setIsEditing(false);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to update profile. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  }, [editData]);

  const handleInputChange = useCallback((field: keyof typeof editData) => 
    (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setEditData(prev => ({ ...prev, [field]: event.target.value }));
    }, []);

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'important';
      case 'staff': return 'informative';
      case 'viewer': return 'subtle';
      default: return 'subtle';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (!user) {
    return (
      <div className={styles.container}>
        <MessageBar intent="error">
          <MessageBarBody>User information not available</MessageBarBody>
        </MessageBar>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {message && (
        <MessageBar intent={message.type}>
          <MessageBarBody>{message.text}</MessageBarBody>
        </MessageBar>
      )}

      <Card className={styles.profileCard}>
        <CardHeader>
          <div className={styles.header}>
            <Avatar
              className={styles.avatar}
              name={user.fullName}
              size={80}
              color="brand"
            />
            <div className={styles.userInfo}>
              <Text className={styles.userName}>{user.fullName}</Text>
              <div className={styles.userRole}>
                <ShieldCheckmarkRegular className={styles.infoIcon} />
                <Badge 
                  appearance="filled" 
                  color={getRoleBadgeColor(user.role)}
                  size="large"
                >
                  {user.role.toUpperCase()}
                </Badge>
              </div>
              <Text size={300} style={{ color: tokens.colorNeutralForeground2 }}>
                @{user.username}
              </Text>
            </div>
          </div>
          
          {!isEditing && (
            <Button
              appearance="subtle"
              icon={<EditRegular />}
              onClick={handleEdit}
            >
              Edit Profile
            </Button>
          )}
        </CardHeader>

        <CardPreview>
          <div className={styles.section}>
            <Text className={styles.sectionTitle}>Personal Information</Text>
            
            {isEditing ? (
              <div className={styles.editForm}>
                <Field label="Full Name">
                  <Input
                    value={editData.fullName}
                    onChange={handleInputChange('fullName')}
                    placeholder="Enter your full name"
                  />
                </Field>
                
                <Field label="Email">
                  <Input
                    type="email"
                    value={editData.email}
                    onChange={handleInputChange('email')}
                    placeholder="Enter your email"
                  />
                </Field>
                
                <Field label="Phone">
                  <Input
                    type="tel"
                    value={editData.phone}
                    onChange={handleInputChange('phone')}
                    placeholder="Enter your phone number"
                  />
                </Field>
                
                <Field label="Bio">
                  <Textarea
                    value={editData.bio}
                    onChange={handleInputChange('bio')}
                    placeholder="Tell us about yourself"
                    rows={3}
                  />
                </Field>

                <div className={styles.formActions}>
                  <Button
                    appearance="subtle"
                    icon={<DismissRegular />}
                    onClick={handleCancel}
                    disabled={isSaving}
                  >
                    Cancel
                  </Button>
                  <Button
                    appearance="primary"
                    icon={<SaveRegular />}
                    onClick={handleSave}
                    disabled={isSaving}
                  >
                    {isSaving ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </div>
            ) : (
              <div className={styles.infoGrid}>
                <div className={styles.infoItem}>
                  <MailRegular className={styles.infoIcon} />
                  <div className={styles.infoContent}>
                    <Text className={styles.infoLabel}>Email</Text>
                    <Text className={styles.infoValue}>{user.email || 'Not provided'}</Text>
                  </div>
                </div>

                <div className={styles.infoItem}>
                  <PhoneRegular className={styles.infoIcon} />
                  <div className={styles.infoContent}>
                    <Text className={styles.infoLabel}>Phone</Text>
                    <Text className={styles.infoValue}>{user.phone || 'Not provided'}</Text>
                  </div>
                </div>

                <div className={styles.infoItem}>
                  <CalendarRegular className={styles.infoIcon} />
                  <div className={styles.infoContent}>
                    <Text className={styles.infoLabel}>Member Since</Text>
                    <Text className={styles.infoValue}>{formatDate(user.createdAt)}</Text>
                  </div>
                </div>

                {lastLoginAt && (
                  <div className={styles.infoItem}>
                    <PersonRegular className={styles.infoIcon} />
                    <div className={styles.infoContent}>
                      <Text className={styles.infoLabel}>Last Login</Text>
                      <Text className={styles.infoValue}>{formatDate(lastLoginAt)}</Text>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          <Divider />

          <div className={styles.section}>
            <Text className={styles.sectionTitle}>Permissions</Text>
            <div className={styles.permissionsList}>
              {permissions.map((permission) => (
                <Badge
                  key={permission}
                  appearance="outline"
                  color="informative"
                  className={styles.permissionBadge}
                >
                  {permission}
                </Badge>
              ))}
            </div>
          </div>
        </CardPreview>
      </Card>
    </div>
  );
});

UserProfile.displayName = 'UserProfile';

export default UserProfile;
