{"version": 3, "file": "no-all-duplicated-branches.js", "sourceRoot": "", "sources": ["../../src/rules/no-all-duplicated-branches.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,0CAA+C;AAC/C,sDAAqD;AACrD,oDAA+E;AAC/E,gDAAwC;AAExC,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,gCAAgC,EAC9B,6FAA6F;YAC/F,mBAAmB,EACjB,+FAA+F;SAClG;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,yFAAyF;YAC3F,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,WAAW,CAAC,IAAmB;gBAC7B,MAAM,MAAM,GAAG,IAA4B,CAAC;gBAE5C,mCAAmC;gBACnC,IAAI,CAAC,IAAA,qBAAa,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC/B,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAA,8BAAiB,EAAC,MAAM,CAAC,CAAC;oBAC7D,IAAI,YAAY,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;wBAC3C,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,kCAAkC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;qBACjF;iBACF;YACH,CAAC;YAED,eAAe,CAAC,IAAmB;gBACjC,MAAM,UAAU,GAAG,IAAgC,CAAC;gBACpD,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,IAAA,kCAAqB,EAAC,UAAU,CAAC,CAAC;gBACxE,IAAI,eAAe,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;oBAC9C,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,kCAAkC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;iBACrF;YACH,CAAC;YAED,qBAAqB,CAAC,IAAmB;gBACvC,MAAM,WAAW,GAAG,IAAsC,CAAC;gBAC3D,MAAM,QAAQ,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;gBACjE,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;oBAC3B,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,qBAAqB,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;iBACzE;YACH,CAAC;SACF,CAAC;QAEF,SAAS,aAAa,CAAC,QAAgD;YACrE,OAAO,CACL,QAAQ,CAAC,MAAM,GAAG,CAAC;gBACnB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACxC,OAAO,IAAA,2BAAa,EAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;gBACzE,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}