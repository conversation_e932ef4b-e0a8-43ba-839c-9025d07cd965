import React, { useCallback, useMemo } from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Title1,
  Body1,
  <PERSON><PERSON>,
  Card,
  CardPreview,
  Text,
} from '@fluentui/react-components';
import {
  ShoppingBag24Regular,
  PaintBrush24Regular,
  Shifts24Regular,
  Person24Regular,
} from '@fluentui/react-icons';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    backgroundColor: tokens.colorNeutralBackground1,
  },
  header: {
    ...shorthands.padding('20px', '24px'),
    backgroundColor: tokens.colorBrandBackground,
    color: tokens.colorNeutralForegroundOnBrand,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    boxShadow: tokens.shadow4,
  },
  headerTitle: {
    color: tokens.colorNeutralForegroundOnBrand,
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
  main: {
    flex: 1,
    ...shorthands.padding('24px'),
    display: 'flex',
    flexDirection: 'column',
    gap: '24px',
  },
  welcomeSection: {
    textAlign: 'center',
    ...shorthands.padding('40px', '20px'),
  },
  cardsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    gap: '20px',
    marginTop: '32px',
  },
  card: {
    cursor: 'pointer',
    transition: 'transform 0.2s ease-in-out',
    ':hover': {
      transform: 'translateY(-2px)',
      boxShadow: tokens.shadow8,
    },
  },
  cardContent: {
    ...shorthands.padding('16px'),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '12px',
    textAlign: 'center',
  },
  icon: {
    fontSize: '48px',
    color: tokens.colorBrandForeground1,
  },
});

const App: React.FC = React.memo(() => {
  const styles = useStyles();

  // Memoized card click handler for performance
  const handleCardClick = useCallback((section: string) => {
    console.log(`Navigating to ${section}`);
    // TODO: Implement navigation
  }, []);

  // Memoized card data to prevent unnecessary re-renders
  const cardData = useMemo(() => [
    {
      id: 'products',
      icon: ShoppingBag24Regular,
      title: 'Products',
      description: 'Manage bags, sarees, paintings and other handcrafts'
    },
    {
      id: 'inventory',
      icon: Shifts24Regular,
      title: 'Inventory',
      description: 'Track stock levels and manage your inventory'
    },
    {
      id: 'customers',
      icon: Person24Regular,
      title: 'Customers',
      description: 'Manage customer information and purchase history'
    },
    {
      id: 'artists',
      icon: PaintBrush24Regular,
      title: 'Artists',
      description: 'Manage artisan profiles and track commissions'
    }
  ], []);

  return (
    <div className={styles.container}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerTitle}>
          <PaintBrush24Regular />
          <Title1>मैथिली विकास कोष Shop Management</Title1>
        </div>
        <Button appearance="subtle" style={{ color: tokens.colorNeutralForegroundOnBrand }}>
          Settings
        </Button>
      </header>

      {/* Main Content */}
      <main className={styles.main}>
        <div className={styles.welcomeSection}>
          <Title1>Welcome to Mithila Handcraft Management</Title1>
          <Body1 style={{ marginTop: '12px', color: tokens.colorNeutralForeground2 }}>
            Manage your beautiful Mithila handcrafts, track inventory, and grow your business
          </Body1>
        </div>

        {/* Feature Cards */}
        <div className={styles.cardsGrid}>
          {cardData.map((card) => {
            const IconComponent = card.icon;
            return (
              <Card
                key={card.id}
                className={styles.card}
                onClick={() => handleCardClick(card.id)}
              >
                <CardPreview>
                  <div className={styles.cardContent}>
                    <IconComponent className={styles.icon} />
                    <Text weight="semibold" size={500}>{card.title}</Text>
                    <Body1>{card.description}</Body1>
                  </div>
                </CardPreview>
              </Card>
            );
          })}
        </div>
      </main>
    </div>
  );
});

App.displayName = 'App';

export default App;
