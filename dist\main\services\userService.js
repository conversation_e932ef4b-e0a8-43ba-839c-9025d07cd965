"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.userService = exports.UserService = void 0;
const crypto = __importStar(require("crypto"));
const database_1 = require("../database/database");
/**
 * User service for authentication and user management
 */
class UserService {
    /**
     * Create a new user
     */
    async createUser(userData) {
        try {
            // Generate salt and hash password
            const salt = crypto.randomBytes(32).toString('hex');
            const passwordHash = this.hashPassword(userData.password, salt);
            const result = database_1.databaseService.execute(`
        INSERT INTO users (username, email, password_hash, salt, role, full_name, phone, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
                userData.username,
                userData.email,
                passwordHash,
                salt,
                userData.role,
                userData.fullName,
                userData.phone || null,
                userData.isActive ? 1 : 0
            ]);
            if (!result.success || !result.data) {
                return { success: false, error: result.error || 'Unknown error' };
            }
            // Get the created user
            const createdUser = await this.getUserById(result.data.lastInsertRowid);
            return createdUser;
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to create user'
            };
        }
    }
    /**
     * Get user by ID
     */
    async getUserById(id) {
        return database_1.databaseService.queryOne(`
      SELECT id, username, email, password_hash, salt, role, full_name, phone, 
             is_active, last_login_at, created_at, updated_at
      FROM users WHERE id = ?
    `, [id]);
    }
    /**
     * Get user by username
     */
    async getUserByUsername(username) {
        return database_1.databaseService.queryOne(`
      SELECT id, username, email, password_hash, salt, role, full_name, phone, 
             is_active, last_login_at, created_at, updated_at
      FROM users WHERE username = ?
    `, [username]);
    }
    /**
     * Get user by email
     */
    async getUserByEmail(email) {
        return database_1.databaseService.queryOne(`
      SELECT id, username, email, password_hash, salt, role, full_name, phone, 
             is_active, last_login_at, created_at, updated_at
      FROM users WHERE email = ?
    `, [email]);
    }
    /**
     * Get all users with pagination and filtering
     */
    async getUsers(filters, sort, page = 1, limit = 50) {
        let sql = `
      SELECT id, username, email, password_hash, salt, role, full_name, phone, 
             is_active, last_login_at, created_at, updated_at
      FROM users
    `;
        let params = [];
        if (filters) {
            const { where, params: whereParams } = database_1.databaseService.buildWhereClause(filters);
            sql += ` ${where}`;
            params = whereParams;
        }
        sql += ` ${database_1.databaseService.buildOrderClause(sort)}`;
        return database_1.databaseService.paginate(sql, params, page, limit);
    }
    /**
     * Update user
     */
    async updateUser(id, userData) {
        try {
            const updates = [];
            const params = [];
            if (userData.username !== undefined) {
                updates.push('username = ?');
                params.push(userData.username);
            }
            if (userData.email !== undefined) {
                updates.push('email = ?');
                params.push(userData.email);
            }
            if (userData.role !== undefined) {
                updates.push('role = ?');
                params.push(userData.role);
            }
            if (userData.fullName !== undefined) {
                updates.push('full_name = ?');
                params.push(userData.fullName);
            }
            if (userData.phone !== undefined) {
                updates.push('phone = ?');
                params.push(userData.phone);
            }
            if (userData.isActive !== undefined) {
                updates.push('is_active = ?');
                params.push(userData.isActive ? 1 : 0);
            }
            if (updates.length === 0) {
                return { success: false, error: 'No fields to update' };
            }
            updates.push('updated_at = datetime("now")');
            params.push(id);
            const result = database_1.databaseService.execute(`
        UPDATE users SET ${updates.join(', ')} WHERE id = ?
      `, params);
            if (!result.success) {
                return { success: false, error: result.error || 'Unknown error' };
            }
            // Get the updated user
            return await this.getUserById(id);
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update user'
            };
        }
    }
    /**
     * Update user password
     */
    async updatePassword(id, newPassword) {
        try {
            const salt = crypto.randomBytes(32).toString('hex');
            const passwordHash = this.hashPassword(newPassword, salt);
            const result = database_1.databaseService.execute(`
        UPDATE users SET password_hash = ?, salt = ?, updated_at = datetime('now') WHERE id = ?
      `, [passwordHash, salt, id]);
            return {
                success: result.success,
                data: result.success,
                error: result.error || undefined
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update password'
            };
        }
    }
    /**
     * Delete user (soft delete by setting inactive)
     */
    async deleteUser(id) {
        const result = database_1.databaseService.execute(`
      UPDATE users SET is_active = 0, updated_at = datetime('now') WHERE id = ?
    `, [id]);
        return {
            success: result.success,
            data: result.success,
            error: result.error || undefined
        };
    }
    /**
     * Authenticate user (alias for authenticate method)
     */
    async authenticateUser(username, password) {
        return this.authenticate(username, password);
    }
    /**
     * Authenticate user
     */
    async authenticate(username, password) {
        try {
            const userResult = await this.getUserByUsername(username);
            if (!userResult.success || !userResult.data) {
                return { success: true, data: null }; // User not found
            }
            const user = userResult.data;
            if (!user.isActive) {
                return { success: false, error: 'User account is inactive' };
            }
            const hashedPassword = this.hashPassword(password, user.salt);
            if (hashedPassword !== user.passwordHash) {
                return { success: true, data: null }; // Invalid password
            }
            // Update last login time
            await database_1.databaseService.execute(`
        UPDATE users SET last_login_at = datetime('now') WHERE id = ?
      `, [user.id]);
            // Remove sensitive data before returning
            const { passwordHash, salt, ...safeUser } = user;
            return { success: true, data: safeUser };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Authentication failed'
            };
        }
    }
    /**
     * Hash password with salt
     */
    hashPassword(password, salt) {
        return crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
    }
    /**
     * Create default admin user if no users exist
     */
    async createDefaultAdmin() {
        try {
            // Check if any users exist
            const countResult = database_1.databaseService.queryOne('SELECT COUNT(*) as count FROM users');
            if (!countResult.success || !countResult.data || countResult.data.count > 0) {
                return { success: true, data: null }; // Users already exist
            }
            // Create default admin user
            const defaultAdmin = {
                username: 'admin',
                email: '<EMAIL>',
                password: 'admin123', // Should be changed on first login
                role: 'admin',
                fullName: 'System Administrator',
                isActive: true
            };
            return await this.createUser(defaultAdmin);
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to create default admin'
            };
        }
    }
}
exports.UserService = UserService;
exports.userService = new UserService();
//# sourceMappingURL=userService.js.map