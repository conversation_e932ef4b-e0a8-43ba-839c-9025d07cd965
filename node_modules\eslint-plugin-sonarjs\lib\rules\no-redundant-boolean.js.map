{"version": 3, "file": "no-redundant-boolean.js", "sourceRoot": "", "sources": ["../../src/rules/no-redundant-boolean.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,0CAA0F;AAC1F,gDAAwC;AAExC,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,wBAAwB,EAAE,wDAAwD;SACnF;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,0CAA0C;YACvD,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,gBAAgB,CAAC,IAAmB;gBAClC,MAAM,UAAU,GAAG,IAAiC,CAAC;gBACrD,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,EAAE;oBAChE,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBACrC,mBAAmB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;iBACvC;YACH,CAAC;YAED,iBAAiB,CAAC,IAAmB;gBACnC,MAAM,UAAU,GAAG,IAAkC,CAAC;gBACtD,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAErC,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,EAAE;oBAChC,mBAAmB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;iBACvC;gBAED,yGAAyG;gBACzG,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;gBACxB,IACE,UAAU,CAAC,QAAQ,KAAK,IAAI;oBAC5B,CAAC,CAAC,IAAA,+BAAuB,EAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,IAAA,qBAAa,EAAC,MAAM,CAAC,CAAC,EAC1F;oBACA,mBAAmB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;iBACvC;YACH,CAAC;YAED,eAAe,CAAC,IAAmB;gBACjC,MAAM,eAAe,GAAG,IAAgC,CAAC;gBACzD,IAAI,eAAe,CAAC,QAAQ,KAAK,GAAG,EAAE;oBACpC,mBAAmB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;iBAC/C;YACH,CAAC;SACF,CAAC;QAEF,SAAS,mBAAmB,CAAC,UAA4D;YACvF,IAAI,IAAA,wBAAgB,EAAC,UAAU,CAAC,EAAE;gBAChC,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,0BAA0B,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;aAC7E;QACH,CAAC;IACH,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}