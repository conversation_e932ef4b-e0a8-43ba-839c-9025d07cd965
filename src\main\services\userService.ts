import * as crypto from 'crypto';
import { databaseService } from '../database/database';
import { User, QueryResult, PaginatedResult, SearchFilters, SortOptions } from '../../shared/types/database';

/**
 * User service for authentication and user management
 */
export class UserService {
  
  /**
   * Create a new user
   */
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'passwordHash' | 'salt'> & { password: string }): Promise<QueryResult<User>> {
    try {
      // Generate salt and hash password
      const salt = crypto.randomBytes(32).toString('hex');
      const passwordHash = this.hashPassword(userData.password, salt);

      const result = databaseService.execute(`
        INSERT INTO users (username, email, password_hash, salt, role, full_name, phone, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        userData.username,
        userData.email,
        passwordHash,
        salt,
        userData.role,
        userData.fullName,
        userData.phone || null,
        userData.isActive ? 1 : 0
      ]);

      if (!result.success || !result.data) {
        return { success: false, error: result.error || 'Unknown error' };
      }

      // Get the created user
      const createdUser = await this.getUserById(result.data.lastInsertRowid as number);
      return createdUser;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create user'
      };
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: number): Promise<QueryResult<User>> {
    return databaseService.queryOne<User>(`
      SELECT id, username, email, password_hash, salt, role, full_name, phone, 
             is_active, last_login_at, created_at, updated_at
      FROM users WHERE id = ?
    `, [id]);
  }

  /**
   * Get user by username
   */
  async getUserByUsername(username: string): Promise<QueryResult<User>> {
    return databaseService.queryOne<User>(`
      SELECT id, username, email, password_hash, salt, role, full_name, phone, 
             is_active, last_login_at, created_at, updated_at
      FROM users WHERE username = ?
    `, [username]);
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<QueryResult<User>> {
    return databaseService.queryOne<User>(`
      SELECT id, username, email, password_hash, salt, role, full_name, phone, 
             is_active, last_login_at, created_at, updated_at
      FROM users WHERE email = ?
    `, [email]);
  }

  /**
   * Get all users with pagination and filtering
   */
  async getUsers(filters?: SearchFilters, sort?: SortOptions, page: number = 1, limit: number = 50): Promise<QueryResult<PaginatedResult<User>>> {
    let sql = `
      SELECT id, username, email, password_hash, salt, role, full_name, phone, 
             is_active, last_login_at, created_at, updated_at
      FROM users
    `;

    let params: any[] = [];

    if (filters) {
      const { where, params: whereParams } = databaseService.buildWhereClause(filters);
      sql += ` ${where}`;
      params = whereParams;
    }

    sql += ` ${databaseService.buildOrderClause(sort)}`;

    return databaseService.paginate<User>(sql, params, page, limit);
  }

  /**
   * Update user
   */
  async updateUser(id: number, userData: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'passwordHash' | 'salt'>>): Promise<QueryResult<User>> {
    try {
      const updates: string[] = [];
      const params: any[] = [];

      if (userData.username !== undefined) {
        updates.push('username = ?');
        params.push(userData.username);
      }

      if (userData.email !== undefined) {
        updates.push('email = ?');
        params.push(userData.email);
      }

      if (userData.role !== undefined) {
        updates.push('role = ?');
        params.push(userData.role);
      }

      if (userData.fullName !== undefined) {
        updates.push('full_name = ?');
        params.push(userData.fullName);
      }

      if (userData.phone !== undefined) {
        updates.push('phone = ?');
        params.push(userData.phone);
      }

      if (userData.isActive !== undefined) {
        updates.push('is_active = ?');
        params.push(userData.isActive ? 1 : 0);
      }

      if (updates.length === 0) {
        return { success: false, error: 'No fields to update' };
      }

      updates.push('updated_at = datetime("now")');
      params.push(id);

      const result = databaseService.execute(`
        UPDATE users SET ${updates.join(', ')} WHERE id = ?
      `, params);

      if (!result.success) {
        return { success: false, error: result.error || 'Unknown error' };
      }

      // Get the updated user
      return await this.getUserById(id);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update user'
      };
    }
  }

  /**
   * Update user password
   */
  async updatePassword(id: number, newPassword: string): Promise<QueryResult<boolean>> {
    try {
      const salt = crypto.randomBytes(32).toString('hex');
      const passwordHash = this.hashPassword(newPassword, salt);

      const result = databaseService.execute(`
        UPDATE users SET password_hash = ?, salt = ?, updated_at = datetime('now') WHERE id = ?
      `, [passwordHash, salt, id]);

      return {
        success: result.success,
        data: result.success,
        error: result.error || undefined
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update password'
      };
    }
  }

  /**
   * Delete user (soft delete by setting inactive)
   */
  async deleteUser(id: number): Promise<QueryResult<boolean>> {
    const result = databaseService.execute(`
      UPDATE users SET is_active = 0, updated_at = datetime('now') WHERE id = ?
    `, [id]);

    return {
      success: result.success,
      data: result.success,
      error: result.error || undefined
    };
  }

  /**
   * Authenticate user (alias for authenticate method)
   */
  async authenticateUser(username: string, password: string): Promise<QueryResult<User | null>> {
    return this.authenticate(username, password);
  }

  /**
   * Authenticate user
   */
  async authenticate(username: string, password: string): Promise<QueryResult<User | null>> {
    try {
      const userResult = await this.getUserByUsername(username);
      
      if (!userResult.success || !userResult.data) {
        return { success: true, data: null }; // User not found
      }

      const user = userResult.data;
      
      if (!user.isActive) {
        return { success: false, error: 'User account is inactive' };
      }

      const hashedPassword = this.hashPassword(password, user.salt);
      
      if (hashedPassword !== user.passwordHash) {
        return { success: true, data: null }; // Invalid password
      }

      // Update last login time
      await databaseService.execute(`
        UPDATE users SET last_login_at = datetime('now') WHERE id = ?
      `, [user.id]);

      // Remove sensitive data before returning
      const { passwordHash, salt, ...safeUser } = user;
      
      return { success: true, data: safeUser as User };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Hash password with salt
   */
  private hashPassword(password: string, salt: string): string {
    return crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  }

  /**
   * Create default admin user if no users exist
   */
  async createDefaultAdmin(): Promise<QueryResult<User | null>> {
    try {
      // Check if any users exist
      const countResult = databaseService.queryOne<{ count: number }>('SELECT COUNT(*) as count FROM users');
      
      if (!countResult.success || !countResult.data || countResult.data.count > 0) {
        return { success: true, data: null }; // Users already exist
      }

      // Create default admin user
      const defaultAdmin = {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123', // Should be changed on first login
        role: 'admin' as const,
        fullName: 'System Administrator',
        isActive: true
      };

      return await this.createUser(defaultAdmin);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create default admin'
      };
    }
  }
}

export const userService = new UserService();
