{"version": 3, "file": "cognitive-complexity.js", "sourceRoot": "", "sources": ["../../src/rules/cognitive-complexity.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,0CAAoE;AACpE,kDAO4B;AAC5B,gDAAwC;AACxC,sCAAuD;AAEvD,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAgB7B,MAAM,OAAO,GACX,mHAAmH,CAAC;AAEtH,MAAM,IAAI,GAAyE;IACjF,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,gBAAgB,EAAE,OAAO;YACzB,YAAY,EAAE,sBAAsB;YACpC,cAAc,EAAE,sBAAsB;SACvC;QACD,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,0DAA0D;YACvE,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE;YAC/B;gBACE,qBAAqB;gBACrB,IAAI,EAAE,CAAC,eAAe,EAAE,QAAQ,CAAC;aAClC;SACF;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAE5B,2BAA2B;QAC3B,MAAM,SAAS,GAAG,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC;QAElF,0DAA0D;QAC1D,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAE5D,uFAAuF;QACvF,MAAM,4BAA4B,GAAuB,IAAI,GAAG,EAAE,CAAC;QAEnE,+DAA+D;QAC/D,MAAM,MAAM,GAAsB,EAAE,CAAC;QAErC,OAAO;YACL,WAAW,EAAE,CAAC,IAAmB,EAAE,EAAE;gBACnC,eAAe,CAAC,IAA6B,CAAC,CAAC;YACjD,CAAC;YACD,gBAAgB,CAAC,IAAmB;gBAClC,eAAe,CAAC,IAA6B,CAAC,CAAC;YACjD,CAAC;YACD,GAAG,CAAC,IAAmB;gBACrB,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACrD,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;iBAC1C;YACH,CAAC;YACD,QAAQ,CAAC,IAAmB;gBAC1B,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACrD,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;oBACzC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACrD;YACH,CAAC;YACD,OAAO,CAAC,IAAsB;gBAC5B,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI;oBACJ,YAAY,EAAE,CAAC;oBACf,YAAY,EAAE,IAAI,GAAG,EAAE;oBACvB,gBAAgB,EAAE,EAAE;iBACrB,CAAC,CAAC;YACL,CAAC;YACD,cAAc,CAAC,IAAmB;gBAChC,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,EAAG,CAAC;gBACxC,IAAI,gBAAgB,EAAE;oBACpB,8EAA8E;oBAC9E,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,gBAAgB;wBAC3B,IAAI,EAAE;4BACJ,gBAAgB,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,MAAM,CACzD,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,UAAU,EAClC,CAAC,CACF;yBACF;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC;YACD,WAAW,CAAC,IAAmB;gBAC7B,gBAAgB,CAAC,IAA4B,CAAC,CAAC;YACjD,CAAC;YACD,YAAY,CAAC,IAAmB;gBAC9B,SAAS,CAAC,IAA6B,CAAC,CAAC;YAC3C,CAAC;YACD,cAAc,CAAC,IAAmB;gBAChC,SAAS,CAAC,IAA+B,CAAC,CAAC;YAC7C,CAAC;YACD,cAAc,CAAC,IAAmB;gBAChC,SAAS,CAAC,IAA+B,CAAC,CAAC;YAC7C,CAAC;YACD,gBAAgB,CAAC,IAAmB;gBAClC,SAAS,CAAC,IAAiC,CAAC,CAAC;YAC/C,CAAC;YACD,cAAc,CAAC,IAAmB;gBAChC,SAAS,CAAC,IAA+B,CAAC,CAAC;YAC7C,CAAC;YACD,eAAe,CAAC,IAAmB;gBACjC,oBAAoB,CAAC,IAAgC,CAAC,CAAC;YACzD,CAAC;YACD,iBAAiB,CAAC,IAAmB;gBACnC,6BAA6B,CAAC,IAAkC,CAAC,CAAC;YACpE,CAAC;YACD,cAAc,CAAC,IAAmB;gBAChC,6BAA6B,CAAC,IAA+B,CAAC,CAAC;YACjE,CAAC;YACD,WAAW,CAAC,IAAmB;gBAC7B,gBAAgB,CAAC,IAA4B,CAAC,CAAC;YACjD,CAAC;YACD,iBAAiB,CAAC,IAAmB;gBACnC,sBAAsB,CAAC,IAAkC,CAAC,CAAC;YAC7D,CAAC;YACD,qBAAqB,CAAC,IAAmB;gBACvC,0BAA0B,CAAC,IAAsC,CAAC,CAAC;YACrE,CAAC;SACF,CAAC;QAEF,SAAS,eAAe,CAAC,IAA2B;YAClD,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,EAAE,YAAY,EAAE,IAAI,GAAG,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC,CAAC;QACxF,CAAC;QAED,SAAS,eAAe,CAAC,IAA2B;YAClD,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,EAAG,CAAC;YACzC,aAAa,CACX,kBAAkB,CAAC,gBAAgB,EACnC,IAAA,wCAA4B,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CACzD,CAAC;QACJ,CAAC;QAED,SAAS,gBAAgB,CAAC,WAAiC;YACzD,MAAM,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC;YAC/B,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,IAAA,yBAAa,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAC3D,uFAAuF;YACvF,IAAI,IAAA,qBAAa,EAAC,MAAM,CAAC,IAAI,MAAM,CAAC,SAAS,KAAK,WAAW,EAAE;gBAC7D,aAAa,CAAC,KAAK,CAAC,CAAC;aACtB;iBAAM;gBACL,uBAAuB,CAAC,KAAK,CAAC,CAAC;aAChC;YAED,wDAAwD;YACxD,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEnE,yCAAyC;YACzC,mDAAmD;YACnD,sBAAsB;YACtB,IAAI,WAAW,CAAC,SAAS,IAAI,CAAC,IAAA,qBAAa,EAAC,WAAW,CAAC,SAAS,CAAC,EAAE;gBAClE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBAClE,MAAM,YAAY,GAAG,IAAA,8BAAkB,EAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAE,CAAC,GAAG,CAAC;gBAC9E,aAAa,CAAC,YAAY,CAAC,CAAC;aAC7B;QACH,CAAC;QAED,SAAS,SAAS,CAAC,IAAmB;YACpC,uBAAuB,CAAC,IAAA,yBAAa,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC;QAED,SAAS,oBAAoB,CAAC,eAAyC;YACrE,uBAAuB,CAAC,IAAA,yBAAa,EAAC,eAAe,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;YACrE,KAAK,MAAM,UAAU,IAAI,eAAe,CAAC,KAAK,EAAE;gBAC9C,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;aACxD;QACH,CAAC;QAED,SAAS,6BAA6B,CACpC,SAA+D;YAE/D,IAAI,SAAS,CAAC,KAAK,EAAE;gBACnB,aAAa,CAAC,IAAA,yBAAa,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;aACtD;QACH,CAAC;QAED,SAAS,gBAAgB,CAAC,WAAiC;YACzD,uBAAuB,CAAC,IAAA,yBAAa,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC;QAED,SAAS,0BAA0B,CAAC,qBAAqD;YACvF,MAAM,gBAAgB,GAAG,IAAA,8BAAkB,EAAC,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAE,CAAC,GAAG,CAAC;YACtF,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;YAC7E,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAC9E,CAAC;QAED,SAAS,sBAAsB,CAAC,iBAA6C;YAC3E,MAAM,oBAAoB,GAAG,IAAA,6BAAuB,EAAC,iBAAiB,CAAC,CAAC;YACxE,IAAI,oBAAoB,IAAI,IAAI,EAAE;gBAChC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,4BAA4B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC7E,OAAO;aACR;YAED,IAAI,qBAAqB,CAAC,iBAAiB,CAAC,EAAE;gBAC5C,OAAO;aACR;YAED,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;gBACxD,MAAM,2BAA2B,GAAG,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;gBAEhF,IAAI,QAAgD,CAAC;gBACrD,KAAK,MAAM,OAAO,IAAI,2BAA2B,EAAE;oBACjD,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,EAAE;wBACvD,MAAM,gBAAgB,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAE,CAAC,GAAG,CAAC;wBACxE,aAAa,CAAC,gBAAgB,CAAC,CAAC;qBACjC;oBACD,QAAQ,GAAG,OAAO,CAAC;iBACpB;aACF;QACH,CAAC;QAED,SAAS,qBAAqB,CAAC,IAAgC;YAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;YAE/C,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,CAAC,SAAS,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;YAEpE,QAAQ,MAAM,EAAE,IAAI,EAAE;gBACpB,qCAAqC;gBACrC,KAAK,oBAAoB;oBACvB,OAAO,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACvE,+BAA+B;gBAC/B,KAAK,sBAAsB;oBACzB,OAAO,CACL,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;wBAC5B,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;wBAC7B,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CACvF,CAAC;gBACJ;oBACE,OAAO,KAAK,CAAC;aAChB;QACH,CAAC;QAED,SAAS,wBAAwB,CAAC,IAAmB;YACnD,IAAI,IAAA,2BAAmB,EAAC,IAAI,CAAC,EAAE;gBAC7B,4BAA4B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACvC,OAAO;oBACL,GAAG,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC;oBACtC,IAAI;oBACJ,GAAG,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC;iBACxC,CAAC;aACH;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,SAAS,uBAAuB,CAAC,QAAiC;YAChE,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC;YACzD,MAAM,eAAe,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnE,CAAC;QAED,SAAS,aAAa,CAAC,QAAiC;YACtD,MAAM,eAAe,GAAG,EAAE,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnE,CAAC;QAED,SAAS,aAAa,CAAC,aAAgC,EAAE,EAAE,GAA4B;YACrF,IAAI,gBAAgB,EAAE;gBACpB,OAAO;aACR;YACD,MAAM,gBAAgB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAClF,IAAI,gBAAgB,GAAG,SAAS,EAAE;gBAChC,MAAM,kBAAkB,GAAoB,UAAU,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;oBAC3E,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,eAAe,CAAC;oBACjD,MAAM,OAAO,GACX,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,WAAW,UAAU,GAAG,CAAC,eAAe,CAAC;oBACnF,OAAO,IAAA,yBAAa,EAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;gBACrD,CAAC,CAAC,CAAC;gBAEH,IAAA,kBAAM,EACJ,OAAO,EACP;oBACE,SAAS,EAAE,kBAAkB;oBAC7B,IAAI,EAAE;wBACJ,gBAAgB;wBAChB,SAAS;qBACV;oBACD,GAAG;iBACJ,EACD,kBAAkB,EAClB,OAAO,EACP,gBAAgB,GAAG,SAAS,CAC7B,CAAC;aACH;QACH,CAAC;IACH,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}