{"version": 3, "file": "no-use-of-empty-return-value.js", "sourceRoot": "", "sources": ["../../src/rules/no-use-of-empty-return-value.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,0CAAmG;AACnG,gDAAwC;AAExC,MAAM,2BAA2B,GAAG,IAAI,GAAG,CAAC;IAC1C,eAAe;IACf,gBAAgB;IAChB,oBAAoB;CACrB,CAAC,CAAC;AAEH,SAAS,iBAAiB,CAAC,QAAuB;IAChD,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IAC5B,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,KAAK,CAAC;KACd;IAED,IAAI,MAAM,CAAC,IAAI,KAAK,mBAAmB,EAAE;QACvC,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC;KACjC;IAED,IAAI,MAAM,CAAC,IAAI,KAAK,oBAAoB,EAAE;QACxC,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,CAAC;KACvE;IAED,IAAI,MAAM,CAAC,IAAI,KAAK,uBAAuB,EAAE;QAC3C,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC;KACjC;IAED,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,qBAAqB;QACrC,MAAM,CAAC,IAAI,KAAK,yBAAyB;QACzC,MAAM,CAAC,IAAI,KAAK,iBAAiB;QACjC,MAAM,CAAC,IAAI,KAAK,iBAAiB;QACjC,MAAM,CAAC,IAAI,KAAK,iBAAiB;QACjC,MAAM,CAAC,IAAI,KAAK,gBAAgB,CACjC,CAAC;AACJ,CAAC;AAED,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,iBAAiB,EACf,qFAAqF;SACxF;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,uEAAuE;YACpF,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,sBAAsB,GAGxB,IAAI,GAAG,EAAE,CAAC;QACd,MAAM,wBAAwB,GAA+B,IAAI,GAAG,EAAE,CAAC;QAEvE,OAAO;YACL,cAAc,CAAC,IAAmB;gBAChC,MAAM,QAAQ,GAAG,IAA+B,CAAC;gBACjD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;oBAChC,OAAO;iBACR;gBACD,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACnF,IAAI,SAAS,IAAI,SAAS,CAAC,QAAQ,EAAE;oBACnC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;oBACpC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC9B,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACpC,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,EAAE;4BACtC,sBAAsB,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;yBACnE;6BAAM,IAAI,UAAU,CAAC,IAAI,KAAK,UAAU,EAAE;4BACzC,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC;4BACjC,IAAI,IAAI,IAAI,CAAC,IAAA,4BAAoB,EAAC,IAAI,CAAC,IAAI,IAAA,iCAAyB,EAAC,IAAI,CAAC,CAAC,EAAE;gCAC3E,sBAAsB,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;6BACxD;yBACF;qBACF;iBACF;YACH,CAAC;YAED,eAAe,CAAC,IAAmB;gBACjC,MAAM,UAAU,GAAG,IAAgC,CAAC;gBACpD,IAAI,UAAU,CAAC,QAAQ,EAAE;oBACvB,MAAM,SAAS,GAAG,CAAC,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;oBACxD,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CACjC,IAAI,CAAC,EAAE,CACL,IAAI,CAAC,IAAI,KAAK,oBAAoB;wBAClC,IAAI,CAAC,IAAI,KAAK,qBAAqB;wBACnC,IAAI,CAAC,IAAI,KAAK,yBAAyB,CAC1C,CAAC;oBAEF,wBAAwB,CAAC,GAAG,CAAC,YAAqC,CAAC,CAAC;iBACrE;YACH,CAAC;YAED,uBAAuB,CAAC,IAAmB;gBACzC,MAAM,SAAS,GAAG,IAAwC,CAAC;gBAC3D,IAAI,SAAS,CAAC,UAAU,EAAE;oBACxB,wBAAwB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;iBACzC;YACH,CAAC;YAED,WAAW,CAAC,IAAmB;gBAC7B,MAAM,IAAI,GAAG,IAGuB,CAAC;gBACrC,IACE,IAAI,CAAC,KAAK;oBACV,IAAI,CAAC,SAAS;oBACd,CAAC,IAAA,wBAAgB,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAC5D;oBACA,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBACpC;YACH,CAAC;YAED,iBAAiB,CAAC,IAAmB;gBACnC,MAAM,eAAe,GAAG,IAAkC,CAAC;gBAC3D,IACE,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,IAAI;oBAC/C,CAAC,2BAA2B,CAAC,GAAG,CAAC,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,EACjF;oBACA,wBAAwB,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;iBAC/C;YACH,CAAC;YAED,cAAc;gBACZ,sBAAsB,CAAC,OAAO,CAAC,CAAC,mBAAmB,EAAE,MAAM,EAAE,EAAE;oBAC7D,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE;wBACtD,OAAO,CAAC,MAAM,CAAC;4BACb,SAAS,EAAE,mBAAmB;4BAC9B,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE;yBAC5B,CAAC,CAAC;qBACJ;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}