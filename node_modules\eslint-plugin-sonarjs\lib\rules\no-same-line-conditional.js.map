{"version": 3, "file": "no-same-line-conditional.js", "sourceRoot": "", "sources": ["../../src/rules/no-same-line-conditional.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,gDAAwC;AACxC,kDAA2D;AAE3D,MAAM,OAAO,GAAG,yDAAyD,CAAC;AAO1E,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,iBAAiB,EAAE,OAAO;YAC1B,YAAY,EAAE,sBAAsB;YACpC,iBAAiB,EAAE,oBAAoB;YACvC,oBAAoB,EAAE,8BAA8B;SACrD;QACD,IAAI,EAAE,SAAS;QACf,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE;YACJ,WAAW,EAAE,wCAAwC;YACrD,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,MAAM,EAAE;YACN;gBACE,qBAAqB;gBACrB,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAO;QACZ,SAAS,eAAe,CAAC,UAA2B;YAClD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;YAC3C,MAAM,mBAAmB,GAAG,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAE/D,mBAAmB,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;gBAC/C,MAAM,WAAW,GAAG,kBAAkB,CAAC,KAAK,CAAC;gBAC7C,MAAM,WAAW,GAAG,kBAAkB,CAAC,SAAS,CAAC;gBACjD,IACE,CAAC,CAAC,WAAW,CAAC,GAAG;oBACjB,CAAC,CAAC,WAAW,CAAC,GAAG;oBACjB,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;oBACvD,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EACvD;oBACA,MAAM,oBAAoB,GAAG,UAAU,CAAC,YAAY,CAAC,WAAW,CAAuB,CAAC;oBACxF,MAAM,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAAC,WAAW,CAAuB,CAAC;oBACrF,IAAA,kBAAM,EACJ,OAAO,EACP;wBACE,SAAS,EAAE,mBAAmB;wBAC9B,GAAG,EAAE,gBAAgB,CAAC,GAAG;wBACzB,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,mBAAmB;gCAC9B,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,OAAO,CAAC;6BAChE;4BACD;gCACE,SAAS,EAAE,sBAAsB;gCACjC,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,KAAK,CAAC,gBAAgB,CACpB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC5C,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAChD;6BACJ;yBACF;qBACF,EACD,CAAC,IAAA,yBAAa,EAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,EACzC,OAAO,CACR,CAAC;iBACH;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,OAAO,EAAE,CAAC,IAAmB,EAAE,EAAE,CAAC,eAAe,CAAE,IAAyB,CAAC,IAAI,CAAC;YAClF,cAAc,EAAE,CAAC,IAAmB,EAAE,EAAE,CACtC,eAAe,CAAE,IAAgC,CAAC,IAAI,CAAC;YACzD,UAAU,EAAE,CAAC,IAAmB,EAAE,EAAE,CAClC,eAAe,CAAE,IAA4B,CAAC,UAAU,CAAC;SAC5D,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,sBAAsB,CAAC,UAA2B;IACzD,OAAO,UAAU,CAAC,MAAM,CAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE;QACxF,MAAM,iBAAiB,GAAG,UAAU,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACvD,IACE,SAAS,CAAC,IAAI,KAAK,aAAa;YAChC,CAAC,CAAC,iBAAiB;YACnB,iBAAiB,CAAC,IAAI,KAAK,aAAa,EACxC;YACA,OAAO,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,GAAG,aAAa,CAAC,CAAC;SAC/E;QACD,OAAO,aAAa,CAAC;IACvB,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,iBAAS,IAAI,CAAC"}