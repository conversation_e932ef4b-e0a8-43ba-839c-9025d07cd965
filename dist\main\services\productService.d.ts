import { Product, QueryResult, PaginatedResult, SearchFilters, SortOptions } from '../../shared/types/database';
/**
 * Product service for managing handcraft products
 */
export declare class ProductService {
    /**
     * Create a new product
     */
    createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<QueryResult<Product>>;
    /**
     * Get product by ID with related data
     */
    getProductById(id: number): Promise<QueryResult<Product & {
        categoryName?: string;
        artistName?: string;
    }>>;
    /**
     * Get product by SKU
     */
    getProductBySku(sku: string): Promise<QueryResult<Product>>;
    /**
     * Get single product by ID (alias for getProductById)
     */
    getProduct(id: number): Promise<QueryResult<Product & {
        categoryName?: string;
        artistName?: string;
    }>>;
    /**
     * Get all products with pagination and filtering
     */
    getProducts(params?: {
        page?: number;
        limit?: number;
        search?: string;
        filters?: SearchFilters;
        sort?: SortOptions;
    }): Promise<QueryResult<PaginatedResult<Product & {
        categoryName?: string;
        artistName?: string;
    }>>>;
    /**
     * Update product
     */
    updateProduct(id: number, productData: Partial<Omit<Product, 'id' | 'createdAt' | 'updatedAt'>>): Promise<QueryResult<Product>>;
    /**
     * Delete product (soft delete by setting inactive)
     */
    deleteProduct(id: number): Promise<QueryResult<boolean>>;
    /**
     * Search products by name or description
     */
    searchProducts(query: string, limit?: number): Promise<QueryResult<Product[]>>;
    /**
     * Get products by category
     */
    getProductsByCategory(categoryId: number, limit?: number): Promise<QueryResult<Product[]>>;
    /**
     * Get products by artist
     */
    getProductsByArtist(artistId: number, limit?: number): Promise<QueryResult<Product[]>>;
    /**
     * Get low stock products
     */
    getLowStockProducts(): Promise<QueryResult<(Product & {
        currentStock: number;
        minStockLevel: number;
    })[]>>;
    /**
     * Generate unique SKU
     */
    generateSku(categoryId: number, artistId: number): Promise<string>;
}
export declare const productService: ProductService;
//# sourceMappingURL=productService.d.ts.map