{"version": 3, "file": "no-extra-arguments.js", "sourceRoot": "", "sources": ["../../src/rules/no-extra-arguments.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,mDAAmD;AAGnD,0CAMwB;AACxB,kDAM4B;AAC5B,gDAAwC;AAExC,MAAM,OAAO,GAAG,kFAAkF,CAAC;AAEnG,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,gBAAgB,EAAE,OAAO;YACzB,YAAY,EAAE,sBAAsB;SACrC;QACD,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,gDAAgD;YAC7D,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,MAAM,EAAE;YACN;gBACE,qBAAqB;gBACrB,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,sBAAsB,GAGvB,EAAE,CAAC;QACR,MAAM,cAAc,GAAuB,IAAI,GAAG,EAAE,CAAC;QACrD,MAAM,cAAc,GAAuB,IAAI,GAAG,EAAE,CAAC;QAErD,OAAO;YACL,wDAAwD;YACxD,cAAc,CAAC,IAAmB;gBAChC,MAAM,QAAQ,GAAG,IAA+B,CAAC;gBACjD,IAAI,IAAA,oBAAY,EAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;oBACjC,MAAM,SAAS,GAAG,OAAO;yBACtB,QAAQ,EAAE;yBACV,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC;oBAC9D,MAAM,UAAU,GAAG,SAAS,IAAI,mBAAmB,CAAC,SAAS,CAAC,CAAC;oBAC/D,IAAI,UAAU,EAAE;wBACd,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,EAAE;4BACtC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;yBAC1C;6BAAM,IAAI,UAAU,CAAC,IAAI,KAAK,UAAU,EAAE;4BACzC,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC;4BACjC,IAAI,IAAI,IAAI,CAAC,IAAA,4BAAoB,EAAC,IAAI,CAAC,IAAI,IAAA,iCAAyB,EAAC,IAAI,CAAC,CAAC,EAAE;gCAC3E,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;6BAC/B;yBACF;qBACF;iBACF;qBAAM,IACL,IAAA,iCAAyB,EAAC,QAAQ,CAAC,MAAM,CAAC;oBAC1C,IAAA,4BAAoB,EAAC,QAAQ,CAAC,MAAM,CAAC,EACrC;oBACA,OAAO;oBACP,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;iBAC1C;YACH,CAAC;YAED,WAAW,CAAC,IAAmB;gBAC7B,MAAM,EAAE,GAAG,IAAmC,CAAC;gBAC/C,IAAI,IAAA,wBAAgB,EAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpF,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC1B;YACH,CAAC;YAED,iDAAiD,CAAC,IAAmB;gBACnE,cAAc,CAAC,IAA2B,CAAC,CAAC;YAC9C,CAAC;YAED,gDAAgD,CAAC,IAAmB;gBAClE,cAAc,CAAC,IAA2B,CAAC,CAAC;YAC9C,CAAC;YAED,cAAc;gBACZ,sBAAsB,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,EAAE;oBAC5D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;wBAC1E,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;qBACrC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;QAEF,SAAS,mBAAmB,CAC1B,SAAmC;YAEnC,IAAI,SAAS,IAAI,SAAS,CAAC,QAAQ,EAAE;gBACnC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;gBACpC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC9B,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACzB;aACF;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,cAAc,CAAC,UAA+B;YACrD,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,EAAE;gBACnC,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,KAAK,UAAU,CAAC,CAAC;gBAC3F,MAAM,UAAU,GAAG,SAAS,IAAI,mBAAmB,CAAC,SAAS,CAAC,CAAC;gBAC/D,iDAAiD;gBACjD,IAAI,CAAC,UAAU,EAAE;oBACf,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,OAAO,EAAE,CAAC;oBACnD,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI,CACvB,IAAI,CAAC,EAAE,CAAC,IAAA,6BAAqB,EAAC,IAAI,CAAC,IAAI,IAAA,4BAAoB,EAAC,IAAI,CAAC,CAClE,CAAC;oBACF,IAAI,EAAE,EAAE;wBACN,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;qBACxB;iBACF;aACF;QACH,CAAC;QAED,SAAS,aAAa,CAAC,QAAiC,EAAE,YAAmC;YAC3F,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC;YAChF,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE;gBACtE,sBAAsB,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;aACzD;QACH,CAAC;QAED,SAAS,WAAW,CAAC,QAAiC,EAAE,YAAmC;YACzF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YAC/C,MAAM,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC;YAC7C,kBAAkB;YAClB,MAAM,iBAAiB;YACrB,6CAA6C;YAC7C,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;gBACpC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;oBAClC,GAAG,WAAW,YAAY,CAAC;YAE7B,kBAAkB;YAClB,MAAM,iBAAiB;YACrB,6CAA6C;YAC7C,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBAC/B,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBAC5B,GAAG,UAAU,OAAO,CAAC;YAEvB,IAAA,kBAAM,EACJ,OAAO,EACP;gBACE,SAAS,EAAE,kBAAkB;gBAC7B,IAAI,EAAE;oBACJ,iBAAiB;oBACjB,iBAAiB;iBAClB;gBACD,IAAI,EAAE,QAAQ,CAAC,MAAM;aACtB,EACD,qBAAqB,CAAC,QAAQ,EAAE,YAAY,CAAC,EAC7C,OAAO,CACR,CAAC;QACJ,CAAC;QAED,SAAS,qBAAqB,CAC5B,QAAiC,EACjC,YAAmC;YAEnC,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YAC/C,MAAM,kBAAkB,GAAoB,EAAE,CAAC;YAC/C,IAAI,WAAW,GAAG,CAAC,EAAE;gBACnB,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC5C,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBACxD,kBAAkB,CAAC,IAAI,CAAC,IAAA,yBAAa,EAAC,QAAQ,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC,CAAC;aAC/E;iBAAM;gBACL,4FAA4F;gBAC5F,MAAM,OAAO,GAAG,IAAA,wCAA4B,EAAC,YAAY,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;gBAC/E,IAAI,OAAO,EAAE;oBACX,kBAAkB,CAAC,IAAI,CAAC,IAAA,yBAAa,EAAC,OAAO,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC,CAAC;iBAC/E;aACF;YACD,2CAA2C;YAC3C,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBAC7C,IAAI,KAAK,IAAI,WAAW,EAAE;oBACxB,kBAAkB,CAAC,IAAI,CAAC,IAAA,+BAAmB,EAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC,CAAC;iBAC1E;YACH,CAAC,CAAC,CAAC;YACH,OAAO,kBAAkB,CAAC;QAC5B,CAAC;IACH,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}