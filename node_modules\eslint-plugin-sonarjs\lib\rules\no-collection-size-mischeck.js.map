{"version": 3, "file": "no-collection-size-mischeck.js", "sourceRoot": "", "sources": ["../../src/rules/no-collection-size-mischeck.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,8DAA4F;AAC5F,gDAAwC;AAExC,MAAM,cAAc,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACrE,MAAM,kBAAkB,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAE9C,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,sBAAsB,EACpB,+FAA+F;YACjG,qBAAqB,EAAE,4CAA4C;SACpE;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;QACf,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE;YACJ,WAAW,EAAE,iEAAiE;YAC9E,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,MAAM,sBAAsB,GAAG,IAAA,0CAAwB,EAAC,QAAQ,CAAC,CAAC;QAClE,OAAO;YACL,gBAAgB,EAAE,CAAC,IAAmB,EAAE,EAAE;gBACxC,MAAM,IAAI,GAAG,IAAiC,CAAC;gBAC/C,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;oBACvC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;oBACtB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;oBACvB,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,kBAAkB,EAAE;wBACzD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;wBACjC,IACE,QAAQ,CAAC,IAAI,KAAK,YAAY;4BAC9B,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;4BAC1C,CAAC,CAAC,sBAAsB,IAAI,YAAY,CAAC,MAAM,EAAE,QAAS,CAAC,CAAC,EAC5D;4BACA,OAAO,CAAC,MAAM,CAAC;gCACb,SAAS,EAAE,wBAAwB;gCACnC,IAAI,EAAE;oCACJ,YAAY,EAAE,QAAQ,CAAC,IAAI;oCAC3B,UAAU,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;iCACpD;gCACD,IAAI;gCACJ,OAAO,EAAE,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;6BACrD,CAAC,CAAC;yBACJ;qBACF;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,aAAa,CAAC,IAAmB;IACxC,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,YAAY,CAAC,IAAmB,EAAE,QAAgC;IACzE,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;IAClD,MAAM,EAAE,GAAG,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/E,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChE,CAAC;AAED,SAAS,aAAa,CACpB,IAA+B,EAC/B,SAAiB,EACjB,OAA+C;IAE/C,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;IAChC,MAAM,aAAa,GAAG,OAAO;SAC1B,aAAa,EAAE;SACf,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAE,CAAC;IAC3D,MAAM,aAAa,GAAG,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;IACpD,OAAO;QACL;YACE,SAAS,EAAE,uBAAuB;YAClC,IAAI,EAAE;gBACJ,SAAS;gBACT,QAAQ,EAAE,aAAa;aACxB;YACD,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,EAAE,aAAa,CAAC;SAC9D;KACF,CAAC;AACJ,CAAC;AAED,iBAAS,IAAI,CAAC"}