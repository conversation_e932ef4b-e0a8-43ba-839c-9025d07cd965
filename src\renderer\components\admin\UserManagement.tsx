import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  CardHeader,
  CardPreview,
  Text,
  Button,
  Input,
  Dropdown,
  Option,
  Badge,
  Avatar,
  Table,
  TableHeader,
  TableRow,
  TableHeaderCell,
  TableBody,
  TableCell,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogBody,
  DialogActions,
  Field,
  MessageBar,
  MessageBarBody,
  Toolbar,
  ToolbarButton,
  makeStyles,
  tokens,
  shorthands,
} from '@fluentui/react-components';
import {
  PersonAddRegular,
  SearchRegular,
  FilterRegular,
  EditRegular,
  DeleteRegular,
  ShieldCheckmarkRegular,
  PersonRegular,
  MailRegular,
  PhoneRegular,
} from '@fluentui/react-icons';
import { useAppSelector } from '../../store/hooks';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: '24px',
    ...shorthands.padding('24px'),
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '16px',
  },
  toolbar: {
    display: 'flex',
    gap: '12px',
    alignItems: 'center',
    marginBottom: '16px',
  },
  searchInput: {
    minWidth: '300px',
  },
  filterDropdown: {
    minWidth: '150px',
  },
  userCard: {
    ...shorthands.padding('16px'),
  },
  userInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
  userDetails: {
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
  },
  userName: {
    fontWeight: '600',
  },
  userMeta: {
    fontSize: '12px',
    color: tokens.colorNeutralForeground2,
  },
  actions: {
    display: 'flex',
    gap: '8px',
  },
  formGrid: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: '16px',
  },
  fullWidth: {
    gridColumn: '1 / -1',
  },
  roleInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
});

interface User {
  id: number;
  username: string;
  email: string;
  fullName: string;
  phone?: string;
  role: 'admin' | 'staff' | 'viewer';
  isActive: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

const UserManagement: React.FC = React.memo(() => {
  const styles = useStyles();
  const { user: currentUser, permissions } = useAppSelector((state) => state.auth);
  
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const [formData, setFormData] = useState({
    username: '',
    email: '',
    fullName: '',
    phone: '',
    role: 'viewer' as 'admin' | 'staff' | 'viewer',
    password: '',
    confirmPassword: '',
  });

  // Check if current user has permission to manage users
  const canManageUsers = permissions.includes('users:create') || permissions.includes('users:update');
  const canDeleteUsers = permissions.includes('users:delete');

  const loadUsers = useCallback(async () => {
    setIsLoading(true);
    try {
      // TODO: Implement actual API call
      // const result = await window.electronAPI.database.getUsers();
      
      // Mock data for now
      const mockUsers: User[] = [
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          fullName: 'System Administrator',
          role: 'admin',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          lastLoginAt: '2024-07-01T10:30:00Z',
        },
        {
          id: 2,
          username: 'staff1',
          email: '<EMAIL>',
          fullName: 'Shop Staff',
          phone: '+91-9876543210',
          role: 'staff',
          isActive: true,
          createdAt: '2024-02-15T00:00:00Z',
          lastLoginAt: '2024-06-30T15:45:00Z',
        },
        {
          id: 3,
          username: 'viewer1',
          email: '<EMAIL>',
          fullName: 'Report Viewer',
          role: 'viewer',
          isActive: false,
          createdAt: '2024-03-01T00:00:00Z',
        },
      ];
      
      setUsers(mockUsers);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to load users' });
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  useEffect(() => {
    let filtered = users;

    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, roleFilter]);

  const handleCreateUser = useCallback(async () => {
    try {
      if (formData.password !== formData.confirmPassword) {
        setMessage({ type: 'error', text: 'Passwords do not match' });
        return;
      }

      // TODO: Implement actual API call
      // await window.electronAPI.database.createUser(formData);
      
      setMessage({ type: 'success', text: 'User created successfully' });
      setIsCreateDialogOpen(false);
      setFormData({
        username: '',
        email: '',
        fullName: '',
        phone: '',
        role: 'viewer',
        password: '',
        confirmPassword: '',
      });
      loadUsers();
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to create user' });
    }
  }, [formData, loadUsers]);

  const handleEditUser = useCallback(async () => {
    if (!selectedUser) return;

    try {
      // TODO: Implement actual API call
      // await window.electronAPI.database.updateUser(selectedUser.id, formData);
      
      setMessage({ type: 'success', text: 'User updated successfully' });
      setIsEditDialogOpen(false);
      setSelectedUser(null);
      loadUsers();
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to update user' });
    }
  }, [selectedUser, formData, loadUsers]);

  const handleDeleteUser = useCallback(async (user: User) => {
    if (user.id === currentUser?.id) {
      setMessage({ type: 'error', text: 'Cannot delete your own account' });
      return;
    }

    try {
      // TODO: Implement actual API call
      // await window.electronAPI.database.deleteUser(user.id);
      
      setMessage({ type: 'success', text: 'User deleted successfully' });
      loadUsers();
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to delete user' });
    }
  }, [currentUser, loadUsers]);

  const openEditDialog = useCallback((user: User) => {
    setSelectedUser(user);
    setFormData({
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      phone: user.phone || '',
      role: user.role,
      password: '',
      confirmPassword: '',
    });
    setIsEditDialogOpen(true);
  }, []);

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'important';
      case 'staff': return 'informative';
      case 'viewer': return 'subtle';
      default: return 'subtle';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (!canManageUsers) {
    return (
      <div className={styles.container}>
        <MessageBar intent="error">
          <MessageBarBody>You don't have permission to manage users.</MessageBarBody>
        </MessageBar>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {message && (
        <MessageBar intent={message.type}>
          <MessageBarBody>{message.text}</MessageBarBody>
        </MessageBar>
      )}

      <div className={styles.header}>
        <Text size={600} weight="semibold">User Management</Text>
        <Dialog open={isCreateDialogOpen} onOpenChange={(_, data) => setIsCreateDialogOpen(data.open)}>
          <DialogTrigger disableButtonEnhancement>
            <Button appearance="primary" icon={<PersonAddRegular />}>
              Add User
            </Button>
          </DialogTrigger>
          <DialogSurface>
            <DialogBody>
              <DialogTitle>Create New User</DialogTitle>
              <DialogContent>
                <div className={styles.formGrid}>
                  <Field label="Username" required>
                    <Input
                      value={formData.username}
                      onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                      placeholder="Enter username"
                    />
                  </Field>
                  
                  <Field label="Full Name" required>
                    <Input
                      value={formData.fullName}
                      onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                      placeholder="Enter full name"
                    />
                  </Field>
                  
                  <Field label="Email" required className={styles.fullWidth}>
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="Enter email address"
                    />
                  </Field>
                  
                  <Field label="Phone">
                    <Input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="Enter phone number"
                    />
                  </Field>
                  
                  <Field label="Role" required>
                    <Dropdown
                      value={formData.role}
                      onOptionSelect={(_, data) => setFormData(prev => ({ ...prev, role: data.optionValue as any }))}
                    >
                      <Option value="admin">Administrator</Option>
                      <Option value="staff">Staff</Option>
                      <Option value="viewer">Viewer</Option>
                    </Dropdown>
                  </Field>
                  
                  <Field label="Password" required>
                    <Input
                      type="password"
                      value={formData.password}
                      onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                      placeholder="Enter password"
                    />
                  </Field>
                  
                  <Field label="Confirm Password" required>
                    <Input
                      type="password"
                      value={formData.confirmPassword}
                      onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      placeholder="Confirm password"
                    />
                  </Field>
                </div>
              </DialogContent>
              <DialogActions>
                <DialogTrigger disableButtonEnhancement>
                  <Button appearance="secondary">Cancel</Button>
                </DialogTrigger>
                <Button appearance="primary" onClick={handleCreateUser}>
                  Create User
                </Button>
              </DialogActions>
            </DialogBody>
          </DialogSurface>
        </Dialog>
      </div>

      <Toolbar>
        <Input
          className={styles.searchInput}
          placeholder="Search users..."
          contentBefore={<SearchRegular />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        
        <Dropdown
          className={styles.filterDropdown}
          placeholder="Filter by role"
          value={roleFilter}
          onOptionSelect={(_, data) => setRoleFilter(data.optionValue || 'all')}
        >
          <Option value="all">All Roles</Option>
          <Option value="admin">Admin</Option>
          <Option value="staff">Staff</Option>
          <Option value="viewer">Viewer</Option>
        </Dropdown>
      </Toolbar>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHeaderCell>User</TableHeaderCell>
              <TableHeaderCell>Role</TableHeaderCell>
              <TableHeaderCell>Status</TableHeaderCell>
              <TableHeaderCell>Last Login</TableHeaderCell>
              <TableHeaderCell>Actions</TableHeaderCell>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className={styles.userInfo}>
                    <Avatar name={user.fullName} size={32} />
                    <div className={styles.userDetails}>
                      <Text className={styles.userName}>{user.fullName}</Text>
                      <Text className={styles.userMeta}>@{user.username} • {user.email}</Text>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    appearance="filled"
                    color={getRoleBadgeColor(user.role)}
                  >
                    {user.role.toUpperCase()}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    appearance="outline"
                    color={user.isActive ? 'success' : 'danger'}
                  >
                    {user.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </TableCell>
                <TableCell>
                  {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}
                </TableCell>
                <TableCell>
                  <div className={styles.actions}>
                    <Button
                      appearance="subtle"
                      icon={<EditRegular />}
                      size="small"
                      onClick={() => openEditDialog(user)}
                    />
                    {canDeleteUsers && user.id !== currentUser?.id && (
                      <Button
                        appearance="subtle"
                        icon={<DeleteRegular />}
                        size="small"
                        onClick={() => handleDeleteUser(user)}
                      />
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
});

UserManagement.displayName = 'UserManagement';

export default UserManagement;
