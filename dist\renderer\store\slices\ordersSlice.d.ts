import { Order } from '../../../shared/types/database';
interface OrdersState {
    selectedOrder: Order | null;
    recentOrders: Order[];
    sortBy: 'order_date' | 'total_amount' | 'status' | 'customer_name';
    sortOrder: 'asc' | 'desc';
    statusFilter: string;
    pageSize: number;
    currentPage: number;
}
export declare const setSelectedOrder: import("@reduxjs/toolkit").ActionCreatorWithPayload<Order | null, "orders/setSelectedOrder">, setSortBy: import("@reduxjs/toolkit").ActionCreatorWithPayload<"order_date" | "total_amount" | "status" | "customer_name", "orders/setSortBy">, setSortOrder: import("@reduxjs/toolkit").ActionCreatorWithPayload<"asc" | "desc", "orders/setSortOrder">, setStatusFilter: import("@reduxjs/toolkit").ActionCreatorWithPayload<string, "orders/setStatusFilter">, setPageSize: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "orders/setPageSize">, setCurrentPage: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "orders/setCurrentPage">;
declare const _default: import("redux").Reducer<OrdersState>;
export default _default;
//# sourceMappingURL=ordersSlice.d.ts.map