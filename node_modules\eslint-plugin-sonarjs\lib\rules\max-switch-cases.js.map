{"version": 3, "file": "max-switch-cases.js", "sourceRoot": "", "sources": ["../../src/rules/max-switch-cases.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,gDAAwC;AAExC,MAAM,wBAAwB,GAAG,EAAE,CAAC;AACpC,IAAI,cAAc,GAAG,wBAAwB,CAAC;AAI9C,MAAM,IAAI,GAAyC;IACjD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,iCAAiC,EAC/B,oGAAoG;SACvG;QACD,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,6DAA6D;YAC1E,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,CAAC;aACX;SACF;KACF;IACD,MAAM,CAAC,OAAO;QACZ,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACrC;QACD,OAAO;YACL,eAAe,EAAE,CAAC,IAAmB,EAAE,EAAE,CACvC,oBAAoB,CAAC,IAAgC,EAAE,OAAO,CAAC;SAClE,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,oBAAoB,CAC3B,eAAyC,EACzC,OAA8C;IAE9C,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,MAAM,CAChD,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAC7E,CAAC;IACF,IAAI,aAAa,CAAC,MAAM,GAAG,cAAc,EAAE;QACzC,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,eAAe,CAAE,CAAC;QAC9E,OAAO,CAAC,MAAM,CAAC;YACb,SAAS,EAAE,mCAAmC;YAC9C,GAAG,EAAE,aAAa,CAAC,GAAG;YACtB,IAAI,EAAE;gBACJ,cAAc,EAAE,aAAa,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAC/C,cAAc,EAAE,cAAc,CAAC,QAAQ,EAAE;aAC1C;SACF,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,aAAa,CAAC,UAA+B;IACpD,OAAO,UAAU,CAAC,IAAI,KAAK,IAAI,CAAC;AAClC,CAAC;AAED,iBAAS,IAAI,CAAC"}