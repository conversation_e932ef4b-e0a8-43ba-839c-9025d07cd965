{"version": 3, "file": "no-inverted-boolean-check.js", "sourceRoot": "", "sources": ["../../src/rules/no-inverted-boolean-check.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,0CAAoD;AACpD,gDAAwC;AAExC,MAAM,iBAAiB,GAAmC;IACxD,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,GAAG,EAAE,IAAI;IACT,GAAG,EAAE,IAAI;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;CACV,CAAC;AAEF,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,mBAAmB,EAAE,2DAA2D;YAChF,yBAAyB,EAAE,uDAAuD;SACnF;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,uCAAuC;YACpD,WAAW,EAAE,KAAK;YAClB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,cAAc,EAAE,IAAI;QACpB,OAAO,EAAE,MAAM;KAChB;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,eAAe,EAAE,CAAC,IAAmB,EAAE,EAAE,CACvC,oBAAoB,CAAC,IAAgC,EAAE,OAAO,CAAC;SAClE,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,oBAAoB,CAC3B,eAAyC,EACzC,OAA+C;IAE/C,IAAI,eAAe,CAAC,QAAQ,KAAK,GAAG,IAAI,IAAA,0BAAkB,EAAC,eAAe,CAAC,QAAQ,CAAC,EAAE;QACpF,MAAM,SAAS,GAA8B,eAAe,CAAC,QAAQ,CAAC;QACtE,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,gBAAgB,EAAE;YACpB,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC/D,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAChB,eAAe,CAAC,MAAM,EAAE,IAAI,KAAK,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC7E,OAAO,CAAC,MAAM,CAAC;gBACb,SAAS,EAAE,qBAAqB;gBAChC,OAAO,EAAE;oBACP;wBACE,SAAS,EAAE,2BAA2B;wBACtC,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,KAAK,CAAC,WAAW,CACf,eAAe,EACf,GAAG,KAAK,GAAG,IAAI,IAAI,gBAAgB,IAAI,KAAK,GAAG,GAAG,EAAE,CACrD;qBACJ;iBACF;gBACD,IAAI,EAAE,EAAE,gBAAgB,EAAE;gBAC1B,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;SACJ;KACF;AACH,CAAC;AAED,iBAAS,IAAI,CAAC"}