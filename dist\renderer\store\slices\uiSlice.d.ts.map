{"version": 3, "file": "uiSlice.d.ts", "sourceRoot": "", "sources": ["../../../../src/renderer/store/slices/uiSlice.ts"], "names": [], "mappings": "AAEA,MAAM,MAAM,KAAK,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;AAC9C,MAAM,MAAM,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;AAE3C,UAAU,YAAY;IACpB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,SAAS,GAAG,OAAO,GAAG,SAAS,GAAG,MAAM,CAAC;IAC/C,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,UAAU,OAAO;IACf,KAAK,EAAE,KAAK,CAAC;IACb,QAAQ,EAAE,QAAQ,CAAC;IACnB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,OAAO,EAAE;QACP,MAAM,EAAE,OAAO,CAAC;QAChB,QAAQ,EAAE,OAAO,CAAC;QAClB,SAAS,EAAE,OAAO,CAAC;QACnB,MAAM,EAAE,OAAO,CAAC;QAChB,SAAS,EAAE,OAAO,CAAC;KACpB,CAAC;IACF,aAAa,EAAE,YAAY,EAAE,CAAC;IAC9B,MAAM,EAAE;QACN,WAAW,EAAE,OAAO,CAAC;QACrB,YAAY,EAAE,OAAO,CAAC;QACtB,SAAS,EAAE,OAAO,CAAC;QACnB,aAAa,EAAE,OAAO,CAAC;KACxB,CAAC;IACF,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE;QACP,QAAQ,EAAE;YACR,QAAQ,EAAE,MAAM,CAAC;YACjB,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC7B,OAAO,EAAE,OAAO,CAAC;SAClB,CAAC;QACF,SAAS,EAAE;YACT,IAAI,EAAE,MAAM,CAAC;YACb,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,MAAM,EAAE;YACN,MAAM,EAAE,MAAM,CAAC;YACf,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SAC7B,CAAC;KACH,CAAC;CACH;AAyGD,eAAO,MACL,QAAQ,6EACR,WAAW,mFACX,aAAa,8EACb,mBAAmB,0FACnB,UAAU;SAvDyC,MAAM,OAAO,CAAC,SAAS,CAAC;WAAS,OAAO;qBAwD3F,eAAe,qHACf,kBAAkB,wFAClB,kBAAkB,mFAClB,SAAS,uIACT,UAAU,wIACV,cAAc,+EACd,cAAc,oFACd,cAAc,oFACd,iBAAiB;cApIH,MAAM;gBACJ,CAAC,MAAM,EAAE,MAAM,CAAC;aACnB,OAAO;6BAmIpB,kBAAkB;UAhIR,MAAM;cACF,MAAM;8BAgIpB,eAAe;YA7HH,MAAM;eACH,CAAC,MAAM,EAAE,MAAM,CAAC;2BA6H/B,YAAY,2EACK,CAAC;;AAEpB,wBAA+B"}