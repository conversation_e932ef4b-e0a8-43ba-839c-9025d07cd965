import type { TSESTree } from '@typescript-eslint/utils';
export declare function isArrowFunctionExpression(node: TSESTree.Node | undefined): node is TSESTree.ArrowFunctionExpression;
export declare function isAssignmentExpression(node: TSESTree.Node | undefined): node is TSESTree.AssignmentExpression;
export declare function isBinaryExpression(node: TSESTree.Node | undefined): node is TSESTree.BinaryExpression;
export declare function isBlockStatement(node: TSESTree.Node | undefined): node is TSESTree.BlockStatement;
export declare function isBooleanLiteral(node: TSESTree.Node | undefined): node is TSESTree.Literal;
export declare function isCallExpression(node: TSESTree.Node | undefined): node is TSESTree.CallExpression;
export declare function isConditionalExpression(node: TSESTree.Node | undefined): node is TSESTree.ConditionalExpression;
export declare function isContinueStatement(node: TSESTree.Node | undefined): node is TSESTree.ContinueStatement;
export declare function isExpressionStatement(node: TSESTree.Node | undefined): node is TSESTree.ExpressionStatement;
export declare function isFunctionDeclaration(node: TSESTree.Node | undefined): node is TSESTree.FunctionDeclaration;
export declare function isFunctionExpression(node: TSESTree.Node | undefined): node is TSESTree.FunctionExpression;
export declare function isIdentifier(node: TSESTree.Node | undefined): node is TSESTree.Identifier;
export declare function isIfStatement(node: TSESTree.Node | undefined): node is TSESTree.IfStatement;
export declare function isLiteral(node: TSESTree.Node | undefined): node is TSESTree.Literal;
export declare function isLogicalExpression(node: TSESTree.Node | undefined): node is TSESTree.LogicalExpression;
export declare function isMemberExpression(node: TSESTree.Node | undefined): node is TSESTree.MemberExpression;
export declare function isModuleDeclaration(node: TSESTree.Node | undefined): node is TSESTree.ExportAllDeclaration | TSESTree.ExportDefaultDeclaration | TSESTree.ExportNamedDeclaration | TSESTree.ImportDeclaration;
export declare function isObjectExpression(node: TSESTree.Node | undefined): node is TSESTree.ObjectExpression;
export declare function isReturnStatement(node: TSESTree.Node | undefined): node is TSESTree.ReturnStatement;
export declare function isThrowStatement(node: TSESTree.Node | undefined): node is TSESTree.ThrowStatement;
export declare function isVariableDeclaration(node: TSESTree.Node | undefined): node is TSESTree.VariableDeclaration;
