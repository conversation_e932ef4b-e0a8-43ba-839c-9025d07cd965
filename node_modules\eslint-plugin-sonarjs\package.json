{"name": "eslint-plugin-sonarjs", "version": "0.25.1", "description": "SonarJS rules for ESLint", "main": "lib/index.js", "types": "lib/index.d.ts", "repository": "**************:SonarSource/eslint-plugin-sonarjs.git", "license": "LGPL-3.0-only", "keywords": ["sonarjs", "eslint", "eslintplugin"], "bugs": {"url": "https://github.com/SonarSource/eslint-plugin-sonarjs/issues"}, "homepage": "https://github.com/SonarSource/eslint-plugin-sonarjs", "engines": {"node": ">=16"}, "files": ["lib", "LICENSE"], "scripts": {"build": "rimraf lib && tsc -d -p tsconfig.json", "test": "jest", "ruling": "ts-node --files ruling/index.ts", "precommit": "pretty-quick --staged", "prepare": "husky install .husky", "prepack": "npm run build", "format": "prettier --write .", "check-format": "prettier --list-different ."}, "peerDependencies": {"eslint": "^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0"}, "devDependencies": {"@babel/core": "7.23.2", "@babel/eslint-parser": "7.22.5", "@babel/plugin-proposal-export-default-from": "7.18.9", "@babel/plugin-proposal-function-bind": "7.18.9", "@babel/preset-env": "7.23.2", "@babel/preset-flow": "7.22.15", "@babel/preset-react": "7.22.15", "@types/eslint": "8.40.0", "@types/jest": "29.5.2", "@types/lodash": "4.14.182", "@types/minimist": "1.2.2", "@types/node": "14.14.31", "@typescript-eslint/parser": "5.30.7", "@typescript-eslint/utils": "5.30.7", "eslint": "8.47.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-notice": "0.9.10", "eslint-plugin-sonarjs": "0.23.0", "husky": "8.0.3", "jest": "29.5.0", "jest-sonar-reporter": "2.0.0", "lodash": "4.17.21", "minimist": "1.2.6", "prettier": "2.7.1", "pretty-quick": "3.1.3", "rimraf": "3.0.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "typescript": "4.7.4"}, "prettier": {"printWidth": 100, "trailingComma": "all", "singleQuote": true, "arrowParens": "avoid", "endOfLine": "lf"}, "jest": {"roots": ["tests", "src"], "collectCoverageFrom": ["src/**/*.ts"], "moduleFileExtensions": ["ts", "js", "json"], "transform": {"^.+\\.ts$": ["ts-jest", {"babelConfig": false}]}, "testMatch": ["<rootDir>/tests/**/*.test.ts"]}}