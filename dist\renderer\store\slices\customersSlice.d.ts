import { Customer } from '../../../shared/types/database';
interface CustomersState {
    selectedCustomer: Customer | null;
    recentCustomers: Customer[];
    sortBy: 'name' | 'email' | 'created_at' | 'total_orders';
    sortOrder: 'asc' | 'desc';
    viewMode: 'grid' | 'list' | 'table';
    pageSize: number;
    currentPage: number;
}
export declare const setSelectedCustomer: import("@reduxjs/toolkit").ActionCreatorWithPayload<Customer | null, "customers/setSelectedCustomer">, setSortBy: import("@reduxjs/toolkit").ActionCreatorWithPayload<"name" | "created_at" | "email" | "total_orders", "customers/setSortBy">, setSortOrder: import("@reduxjs/toolkit").ActionCreatorWithPayload<"asc" | "desc", "customers/setSortOrder">, setViewMode: import("@reduxjs/toolkit").ActionCreatorWithPayload<"grid" | "table" | "list", "customers/setViewMode">, setPageSize: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "customers/setPageSize">, setCurrentPage: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "customers/setCurrentPage">;
declare const _default: import("redux").Reducer<CustomersState>;
export default _default;
//# sourceMappingURL=customersSlice.d.ts.map