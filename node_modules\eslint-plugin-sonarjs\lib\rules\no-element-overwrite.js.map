{"version": 3, "file": "no-element-overwrite.js", "sourceRoot": "", "sources": ["../../src/rules/no-element-overwrite.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,sDAAqD;AACrD,0CAOwB;AACxB,kDAA2D;AAC3D,gDAAwC;AAExC,MAAM,OAAO,GACX,2FAA2F,CAAC;AAE9F,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,mBAAmB,EAAE,OAAO;YAC5B,YAAY,EAAE,sBAAsB;SACrC;QACD,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,4DAA4D;YACzE,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,MAAM,EAAE;YACN;gBACE,qBAAqB;gBACrB,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,UAAU,CAAC,IAAmB;gBAC5B,MAAM,UAAU,GAAG,IAA2B,CAAC;gBAC/C,eAAe,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACzC,CAAC;YAED,cAAc,CAAC,IAAmB;gBAChC,MAAM,KAAK,GAAG,IAA+B,CAAC;gBAC9C,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;YAED,OAAO,CAAC,IAAmB;gBACzB,MAAM,OAAO,GAAG,IAAwB,CAAC;gBACzC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC;SACF,CAAC;QAEF,SAAS,eAAe,CAAC,UAA4C;YACnE,MAAM,QAAQ,GAAyC,IAAI,GAAG,EAAE,CAAC;YACjE,IAAI,UAAqC,CAAC;YAC1C,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC7B,MAAM,aAAa,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;gBAClD,IAAI,aAAa,EAAE;oBACjB,IACE,UAAU;wBACV,CAAC,IAAA,2BAAa,EAAC,aAAa,CAAC,cAAc,EAAE,UAAU,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,EACjF;wBACA,QAAQ,CAAC,KAAK,EAAE,CAAC;qBAClB;oBACD,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;oBACjE,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,EAAE;wBACnD,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC;wBACxD,MAAM,kBAAkB,GAAG;4BACzB,IAAA,yBAAa,EAAC,oBAAoB,EAAE,oBAAoB,EAAE,gBAAgB,CAAC;yBAC5E,CAAC;wBACF,IAAA,kBAAM,EACJ,OAAO,EACP;4BACE,IAAI,EAAE,aAAa,CAAC,IAAI;4BACxB,SAAS,EAAE,qBAAqB;4BAChC,IAAI,EAAE;gCACJ,KAAK,EAAE,aAAa,CAAC,UAAU;gCAC/B,IAAI,EAAE,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;6BAC5C;yBACF,EACD,kBAAkB,EAClB,OAAO,CACR,CAAC;qBACH;oBACD,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;oBACtD,UAAU,GAAG,aAAa,CAAC,cAAc,CAAC;iBAC3C;qBAAM;oBACL,QAAQ,CAAC,KAAK,EAAE,CAAC;iBAClB;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,SAAS,gBAAgB,CAAC,IAAmB;YAC3C,IAAI,IAAA,6BAAqB,EAAC,IAAI,CAAC,EAAE;gBAC/B,OAAO,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACtF;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,kBAAkB,CAAC,IAAmB;YAC7C,aAAa;YACb,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,IAAA,0BAAkB,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACnF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;gBAC7B,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1C,IAAI,KAAK,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;oBACtD,OAAO;wBACL,cAAc,EAAE,IAAI,CAAC,MAAM;wBAC3B,UAAU,EAAE,KAAK;wBACjB,IAAI;qBACL,CAAC;iBACH;aACF;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,qBAAqB,CAAC,IAAmB;YAChD,IAAI,IAAA,wBAAgB,EAAC,IAAI,CAAC,IAAI,IAAA,0BAAkB,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC7D,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;gBACnC,IAAI,IAAA,oBAAY,EAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;oBACzC,MAAM,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAChD,MAAM,SAAS,GAAG,UAAU,KAAK,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC;oBACtE,MAAM,SAAS,GAAG,UAAU,KAAK,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC;oBAEtE,IAAI,SAAS,IAAI,SAAS,EAAE;wBAC1B,MAAM,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5C,IAAI,GAAG,EAAE;4BACP,OAAO;gCACL,cAAc,EAAE,cAAc,CAAC,MAAM;gCACrC,UAAU,EAAE,GAAG;gCACf,IAAI;6BACL,CAAC;yBACH;qBACF;iBACF;aACF;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,YAAY,CAAC,IAAmB;YACvC,IAAI,IAAA,iBAAS,EAAC,IAAI,CAAC,EAAE;gBACnB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;gBACvB,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aAC3F;iBAAM,IAAI,IAAA,oBAAY,EAAC,IAAI,CAAC,EAAE;gBAC7B,OAAO,IAAI,CAAC,IAAI,CAAC;aAClB;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,MAAM,CAAC,KAAoB,EAAE,UAA+B;YACnE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM,gBAAgB,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAEvE,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACxD,IAAI,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC7B,KACE,IAAI,eAAe,GAAG,KAAK,EAAE,UAAU,GAAG,CAAC,EAC3C,eAAe,GAAG,gBAAgB,CAAC,MAAM,IAAI,UAAU,GAAG,WAAW,CAAC,MAAM,EAC5E,eAAe,EAAE,EAAE,UAAU,EAAE,EAC/B;wBACA,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,EAAE;4BACnE,MAAM;yBACP;6BAAM,IAAI,UAAU,KAAK,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;4BAChD,OAAO,IAAI,CAAC;yBACb;qBACF;iBACF;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;YAEH,OAAO,UAAU,KAAK,SAAS,CAAC;QAClC,CAAC;IACH,CAAC;CACF,CAAC;AAEF,SAAS,EAAE,CAAC,MAA0B,EAAE,MAA0B;IAChE,OAAO,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;AACvC,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAmB;IAC7C,OAAO,IAAA,8BAAsB,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC;AAC/D,CAAC;AAQD,iBAAS,IAAI,CAAC"}