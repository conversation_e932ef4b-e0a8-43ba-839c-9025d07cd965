{"version": 3, "file": "conditions.js", "sourceRoot": "", "sources": ["../../src/utils/conditions.ts"], "names": [], "mappings": ";;;AAoBA,mCAAwC;AAExC,kFAAkF;AAClF,SAAgB,iBAAiB,CAAC,IAA0B;IAC1D,MAAM,QAAQ,GAAyB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzD,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IAE/B,OAAO,SAAS,EAAE;QAChB,IAAI,IAAA,qBAAa,EAAC,SAAS,CAAC,EAAE;YAC5B,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACpC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;SACjC;aAAM;YACL,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACzB,YAAY,GAAG,IAAI,CAAC;YACpB,MAAM;SACP;KACF;IAED,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;AACpC,CAAC;AAjBD,8CAiBC;AAED,qEAAqE;AACrE,SAAgB,qBAAqB,CAAC,IAA8B;IAClE,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK;SACxB,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACxB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,eAAe,GAAG,IAAI,CAAC;SACxB;QACD,uFAAuF;QACvF,sCAAsC;QACtC,MAAM,MAAM,GAAG,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/C,OAAO,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAChD,CAAC,CAAC;SACD,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;IACtD,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;AACvC,CAAC;AAdD,sDAcC;AAED,iDAAiD;AACjD,SAAgB,gBAAgB,CAAC,KAA2B;IAC1D,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB;QAC1E,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,KAAK,CAAC;AACZ,CAAC;AAJD,4CAIC"}