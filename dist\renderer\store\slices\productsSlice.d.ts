import { Product } from '../../../shared/types/database';
interface ProductsState {
    selectedProduct: Product | null;
    recentlyViewed: Product[];
    favorites: number[];
    bulkSelection: number[];
    sortBy: 'name' | 'price' | 'created_at' | 'stock_quantity';
    sortOrder: 'asc' | 'desc';
    viewMode: 'grid' | 'list' | 'table';
    pageSize: number;
    currentPage: number;
}
export declare const setSelectedProduct: import("@reduxjs/toolkit").ActionCreatorWithPayload<Product | null, "products/setSelectedProduct">, addToFavorites: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "products/addToFavorites">, removeFromFavorites: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "products/removeFromFavorites">, toggleFavorite: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "products/toggleFavorite">, setBulkSelection: import("@reduxjs/toolkit").ActionCreatorWithPayload<number[], "products/setBulkSelection">, addToBulkSelection: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "products/addToBulkSelection">, removeFromBulkSelection: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "products/removeFromBulkSelection">, toggleBulkSelection: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "products/toggleBulkSelection">, clearBulkSelection: import("@reduxjs/toolkit").ActionCreatorWithoutPayload<"products/clearBulkSelection">, setSortBy: import("@reduxjs/toolkit").ActionCreatorWithPayload<"name" | "price" | "created_at" | "stock_quantity", "products/setSortBy">, setSortOrder: import("@reduxjs/toolkit").ActionCreatorWithPayload<"asc" | "desc", "products/setSortOrder">, toggleSortOrder: import("@reduxjs/toolkit").ActionCreatorWithoutPayload<"products/toggleSortOrder">, setViewMode: import("@reduxjs/toolkit").ActionCreatorWithPayload<"grid" | "table" | "list", "products/setViewMode">, setPageSize: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "products/setPageSize">, setCurrentPage: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "products/setCurrentPage">, clearRecentlyViewed: import("@reduxjs/toolkit").ActionCreatorWithoutPayload<"products/clearRecentlyViewed">, resetProductsState: import("@reduxjs/toolkit").ActionCreatorWithoutPayload<"products/resetProductsState">;
declare const _default: import("redux").Reducer<ProductsState>;
export default _default;
//# sourceMappingURL=productsSlice.d.ts.map