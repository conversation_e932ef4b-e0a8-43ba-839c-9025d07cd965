{"version": 3, "file": "no-duplicated-branches.js", "sourceRoot": "", "sources": ["../../src/rules/no-duplicated-branches.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,0CAAiE;AACjE,sDAAqD;AACrD,oDAAiG;AACjG,kDAA2D;AAC3D,gDAAwC;AAExC,MAAM,OAAO,GACX,wFAAwF,CAAC;AAE3F,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,oBAAoB,EAAE,OAAO;YAC7B,YAAY,EAAE,sBAAsB;SACrC;QACD,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,yFAAyF;YAC3F,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,MAAM,EAAE;YACN;gBACE,qBAAqB;gBACrB,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,WAAW,CAAC,IAAmB;gBAC7B,gBAAgB,CAAC,IAA4B,CAAC,CAAC;YACjD,CAAC;YACD,eAAe,CAAC,IAAmB;gBACjC,oBAAoB,CAAC,IAAgC,CAAC,CAAC;YACzD,CAAC;SACF,CAAC;QAEF,SAAS,gBAAgB,CAAC,MAA4B;YACpD,IAAI,IAAA,qBAAa,EAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBAChC,OAAO;aACR;YACD,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAA,8BAAiB,EAAC,MAAM,CAAC,CAAC;YAE7D,IAAI,2BAA2B,CAAC,QAAQ,EAAE,YAAY,CAAC,EAAE;gBACvD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACrF,OAAO;aACR;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxC,IAAI,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC1B,IAAI,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;4BAC/C,MAAM;yBACP;qBACF;iBACF;aACF;QACH,CAAC;QAED,SAAS,oBAAoB,CAAC,UAAoC;YAChE,MAAM,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC;YAC7B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,kCAAqB,EAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAChC,CAAC,CAAC,EAAE,CAAC,IAAA,6BAAgB,EAAC,0BAA0B,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAC3E,CAAC;YACF,MAAM,iBAAiB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAC9C,IAAA,6BAAgB,EAAC,0BAA0B,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAC3D,CAAC;YAEF,IAAI,2BAA2B,CAAC,iBAAiB,EAAE,eAAe,CAAC,EAAE;gBACnE,aAAa;qBACV,KAAK,CAAC,CAAC,CAAC;qBACR,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;gBAC7E,OAAO;aACR;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,MAAM,uBAAuB,GAAG,IAAA,6BAAgB,EAC9C,0BAA0B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAChD,CAAC;gBAEF,IAAI,eAAe,CAAC,uBAAuB,CAAC,EAAE;oBAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC1B,MAAM,wBAAwB,GAAG,IAAA,6BAAgB,EAC/C,0BAA0B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAChD,CAAC;wBAEF,IACE,IAAA,2BAAa,EACX,uBAAuB,EACvB,wBAAwB,EACxB,OAAO,CAAC,aAAa,EAAE,CACxB,EACD;4BACA,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;4BACxC,MAAM;yBACP;qBACF;iBACF;aACF;QACH,CAAC;QAED,SAAS,eAAe,CAAC,KAA2B;YAClD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpB,MAAM,MAAM,GAAG;oBACb,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC9C,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;iBAC9D,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;gBAC9D,OAAO,CACL,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CACvF,CAAC;aACH;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,iBAAiB,CAAC,CAAqB,EAAE,CAAqB;YACrE,MAAM,UAAU,GAAG,IAAA,2BAAa,EAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;YAChE,IAAI,UAAU,IAAI,CAAC,CAAC,GAAG,EAAE;gBACvB,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;aAC7B;YACD,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,SAAS,0BAA0B,CAAC,KAA2B;YAC7D,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,IAAA,wBAAgB,EAAC,IAAI,CAAC,EAAE;oBAC1B,OAAO,IAAI,CAAC,IAAI,CAAC;iBAClB;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,2BAA2B,CAClC,QAAgD,EAChD,eAAwB;YAExB,OAAO,CACL,CAAC,eAAe;gBAChB,QAAQ,CAAC,MAAM,GAAG,CAAC;gBACnB,QAAQ;qBACL,KAAK,CAAC,CAAC,CAAC;qBACR,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,IAAA,2BAAa,EAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAC7F,CAAC;QACJ,CAAC;QAED,SAAS,WAAW,CAAC,IAAmB,EAAE,cAA6B,EAAE,IAAY;YACnF,MAAM,iBAAiB,GAAG,cAAc,CAAC,GAA8B,CAAC;YACxE,IAAA,kBAAM,EACJ,OAAO,EACP;gBACE,SAAS,EAAE,sBAAsB;gBACjC,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3D,IAAI;aACL,EACD,CAAC,IAAA,yBAAa,EAAC,iBAAiB,EAAE,iBAAiB,EAAE,UAAU,CAAC,CAAC,EACjE,OAAO,CACR,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}