import { InventoryItem } from '../../../shared/types/database';
interface InventoryState {
    selectedItem: InventoryItem | null;
    lowStockThreshold: number;
    sortBy: 'product_name' | 'current_stock' | 'last_updated';
    sortOrder: 'asc' | 'desc';
    showLowStockOnly: boolean;
    pageSize: number;
    currentPage: number;
}
export declare const setSelectedItem: import("@reduxjs/toolkit").ActionCreatorWithPayload<import("../../../shared/types/database").Inventory | null, "inventory/setSelectedItem">, setLowStockThreshold: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "inventory/setLowStockThreshold">, setSortBy: import("@reduxjs/toolkit").ActionCreatorWithPayload<"product_name" | "current_stock" | "last_updated", "inventory/setSortBy">, setSortOrder: import("@reduxjs/toolkit").ActionCreatorWithPayload<"asc" | "desc", "inventory/setSortOrder">, setShowLowStockOnly: import("@reduxjs/toolkit").ActionCreatorWithPayload<boolean, "inventory/setShowLowStockOnly">, setPageSize: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "inventory/setPageSize">, setCurrentPage: import("@reduxjs/toolkit").ActionCreatorWithPayload<number, "inventory/setCurrentPage">;
declare const _default: import("redux").Reducer<InventoryState>;
export default _default;
//# sourceMappingURL=inventorySlice.d.ts.map