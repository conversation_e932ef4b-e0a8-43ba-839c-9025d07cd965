{"version": 3, "file": "no-identical-functions.js", "sourceRoot": "", "sources": ["../../src/rules/no-identical-functions.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,sDAAqD;AACrD,kDAAyF;AACzF,gDAAwC;AAExC,MAAM,iBAAiB,GAAG,CAAC,CAAC;AAO5B,MAAM,OAAO,GACX,+FAA+F,CAAC;AAIlG,MAAM,IAAI,GAAyC;IACjD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,kBAAkB,EAAE,OAAO;YAC3B,YAAY,EAAE,sBAAsB;SACrC;QACD,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,qDAAqD;YAClE,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE;YAC/B;gBACE,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,SAAS,GAAyE,EAAE,CAAC;QAC3F,MAAM,QAAQ,GACZ,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC;QAElF,OAAO;YACL,mBAAmB,CAAC,IAAmB;gBACrC,aAAa,CAAC,IAAoC,CAAC,CAAC;YACtD,CAAC;YACD,gFAAgF,EAAE,CAChF,IAAmB,EACnB,EAAE;gBACF,aAAa,CAAC,IAAmC,CAAC,CAAC;YACrD,CAAC;YACD,0FAA0F,EAAE,CAC1F,IAAmB,EACnB,EAAE;gBACF,aAAa,CAAC,IAAwC,CAAC,CAAC;YAC1D,CAAC;YAED,cAAc;gBACZ,gBAAgB,EAAE,CAAC;YACrB,CAAC;SACF,CAAC;QAEF,SAAS,aAAa,CAAC,IAAkB;YACvC,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC1B,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;aACzD;QACH,CAAC;QAED,SAAS,gBAAgB;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,MAAM,mBAAmB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC1B,MAAM,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;oBAE/C,IACE,IAAA,2BAAa,EACX,mBAAmB,CAAC,IAAI,EACxB,gBAAgB,CAAC,IAAI,EACrB,OAAO,CAAC,aAAa,EAAE,CACxB;wBACD,gBAAgB,CAAC,GAAG,EACpB;wBACA,MAAM,GAAG,GAAG,IAAA,wCAA4B,EACtC,mBAAmB,EACnB,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EACnB,OAAO,CACR,CAAC;wBACF,MAAM,mBAAmB,GAAG,IAAA,wCAA4B,EACtD,gBAAgB,EAChB,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EACnB,OAAO,CACR,CAAC;wBACF,MAAM,kBAAkB,GAAG;4BACzB,IAAA,yBAAa,EAAC,mBAAmB,EAAE,mBAAmB,EAAE,yBAAyB,CAAC;yBACnF,CAAC;wBACF,IAAA,kBAAM,EACJ,OAAO,EACP;4BACE,SAAS,EAAE,oBAAoB;4BAC/B,IAAI,EAAE;gCACJ,IAAI,EAAE,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;6BACtC;4BACD,GAAG;yBACJ,EACD,kBAAkB,EAClB,OAAO,CACR,CAAC;wBACF,MAAM;qBACP;iBACF;aACF;QACH,CAAC;QAED,SAAS,WAAW,CAAC,IAAmB;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAEvD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,EAAE;gBAChD,MAAM,CAAC,KAAK,EAAE,CAAC;aAChB;YAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,EAAE;gBAChE,MAAM,CAAC,GAAG,EAAE,CAAC;aACd;YAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC3C,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;gBAExD,OAAO,QAAQ,GAAG,SAAS,GAAG,CAAC,IAAI,QAAQ,CAAC;aAC7C;YAED,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}