{"version": 3, "file": "no-identical-conditions.js", "sourceRoot": "", "sources": ["../../src/rules/no-identical-conditions.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,sDAAqD;AACrD,kDAA2D;AAC3D,gDAAwC;AAExC,MAAM,0BAA0B,GAAG,uDAAuD,CAAC;AAC3F,MAAM,qBAAqB,GAAG,+CAA+C,CAAC;AAE9E,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,mBAAmB,EAAE,0BAA0B;YAC/C,cAAc,EAAE,qBAAqB;YACrC,YAAY,EAAE,sBAAsB;SACrC;QACD,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,sFAAsF;YACxF,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;QACD,MAAM,EAAE;YACN;gBACE,qBAAqB;gBACrB,IAAI,EAAE,CAAC,eAAe,CAAC;aACxB;SACF;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO;YACL,WAAW,CAAC,IAAmB;gBAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,IAA4B,CAAC;gBAC9C,MAAM,iBAAiB,GACrB,IAAI,CAAC,IAAI,KAAK,mBAAmB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI;oBACzD,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAEb,IAAI,OAAO,GAAG,IAAI,CAAC;gBACnB,IAAI,eAAe,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC/E,OAAO,OAAO,CAAC,MAAM,EAAE,IAAI,KAAK,aAAa,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,KAAK,OAAO,EAAE;oBACrF,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;oBAEzB,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBAClE,eAAe,GAAG,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CACjD,UAAU,CAAC,MAAM,CACf,SAAS,CAAC,EAAE,CACV,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CACzC,QAAQ,CAAC,gBAAgB,EAAE,SAAS,EAAE,UAAU,CAAC,CAClD,CACJ,CACF,CAAC;oBAEF,IAAI,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;wBAC/D,IAAA,kBAAM,EACJ,OAAO,EACP;4BACE,SAAS,EAAE,qBAAqB;4BAChC,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE;4BAC3C,IAAI,EAAE,IAAI;yBACX,EACD,CAAC,IAAA,yBAAa,EAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,EAC/D,0BAA0B,CAC3B,CAAC;wBACF,MAAM;qBACP;iBACF;YACH,CAAC;YACD,eAAe,CAAC,IAAmB;gBACjC,MAAM,UAAU,GAAG,IAAgC,CAAC;gBACpD,MAAM,aAAa,GAA0B,EAAE,CAAC;gBAChD,KAAK,MAAM,UAAU,IAAI,UAAU,CAAC,KAAK,EAAE;oBACzC,IAAI,UAAU,CAAC,IAAI,EAAE;wBACnB,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC;wBAC5B,MAAM,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CACtD,IAAA,2BAAa,EAAC,IAAI,EAAE,YAAY,EAAE,UAAU,CAAC,CAC9C,CAAC;wBACF,IAAI,aAAa,EAAE;4BACjB,IAAA,kBAAM,EACJ,OAAO,EACP;gCACE,SAAS,EAAE,gBAAgB;gCAC3B,IAAI,EAAE;oCACJ,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;iCACnC;gCACD,IAAI,EAAE,IAAI;6BACX,EACD,CAAC,IAAA,yBAAa,EAAC,aAAa,CAAC,GAAG,EAAE,aAAa,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,EACjE,qBAAqB,CACtB,CAAC;yBACH;6BAAM;4BACL,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBAC1B;qBACF;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,MAAM,SAAS,GAAG,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1D,MAAM,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAE3D,SAAS,sBAAsB,CAC7B,QAA4B,EAC5B,IAAmB;IAEnB,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAmB,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;QACnE,OAAO;YACL,GAAG,sBAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC;YAC9C,GAAG,sBAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC;SAChD,CAAC;KACH;IACD,OAAO,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC;AAED,SAAS,QAAQ,CACf,KAAsB,EACtB,MAAuB,EACvB,UAA+B;IAE/B,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;IAEhF,SAAS,UAAU,CACjB,KAAoB,EACpB,MAAqB,EACrB,UAA+B;QAE/B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE;YAC9B,OAAO,KAAK,CAAC;SACd;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE;YACtC,MAAM,OAAO,GAAG,MAAoC,CAAC;YACrD,IACE,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC;gBACpD,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,EACnC;gBACA,OAAO,CACL,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC;oBAC/C,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;oBACrD,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC;wBAChD,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CACrD,CAAC;aACH;SACF;QAED,OAAO,IAAA,2BAAa,EAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAClD,CAAC;AACH,CAAC;AAED,iBAAS,IAAI,CAAC"}