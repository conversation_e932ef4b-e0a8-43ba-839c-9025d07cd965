{"version": 3, "file": "prefer-single-boolean-return.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-single-boolean-return.ts"], "names": [], "mappings": ";AAqBA,0CAKwB;AACxB,gDAAwC;AAExC,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,yBAAyB,EAAE,8DAA8D;YACzF,OAAO,EAAE,sCAAsC;YAC/C,WAAW,EAAE,sDAAsD;YACnE,cAAc,EACZ,kFAAkF;SACrF;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE;YACJ,WAAW,EACT,sFAAsF;YACxF,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,WAAW,CAAC,IAA0B;gBACpC;gBACE,mBAAmB;gBACnB,CAAC,IAAA,qBAAa,EAAC,IAAI,CAAC,MAAM,CAAC;oBAC3B,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC;oBAC/B,uBAAuB,CAAC,IAAI,CAAC,EAC7B;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,2BAA2B;wBACtC,IAAI;wBACJ,OAAO,EAAE,aAAa,CAAC,IAAI,CAAC;qBAC7B,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;QAEF,SAAS,uBAAuB,CAAC,IAA0B;YACzD,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACvC;YAED,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;YACxB,IAAI,MAAM,EAAE,IAAI,KAAK,gBAAgB,EAAE;gBACrC,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;gBACjE,OAAO,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;aACnE;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,cAAc,CAAC,SAAyC;YAC/D,OAAO,CACL,SAAS,KAAK,SAAS;gBACvB,CAAC,8BAA8B,CAAC,SAAS,CAAC,IAAI,4BAA4B,CAAC,SAAS,CAAC,CAAC,CACvF,CAAC;QACJ,CAAC;QAED,SAAS,8BAA8B,CAAC,SAA6B;YACnE,OAAO,CACL,IAAA,wBAAgB,EAAC,SAAS,CAAC;gBAC3B,SAAS,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;gBAC3B,4BAA4B,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAChD,CAAC;QACJ,CAAC;QAED,SAAS,4BAA4B,CAAC,SAAwB;YAC5D,+EAA+E;YAC/E,OAAO,IAAA,yBAAiB,EAAC,SAAS,CAAC,IAAI,IAAA,wBAAgB,EAAC,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,CAAC;QAC3F,CAAC;QAED,SAAS,aAAa,CAAC,MAA4B;YACjD,MAAM,MAAM,GAAG,CAAC,SAAiB,EAAE,EAAE;gBACnC,OAAO,CAAC,KAAyB,EAAE,EAAE;oBACnC,MAAM,YAAY,GAAG,UAAU,SAAS,GAAG,CAAC;oBAC5C,IAAI,MAAM,CAAC,SAAS,EAAE;wBACpB,OAAO,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;qBAChD;yBAAM;wBACL,MAAM,MAAM,GAAG,MAAM,CAAC,MAAiC,CAAC;wBACxD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;wBACnE,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;wBAChD,MAAM,KAAK,GAAqB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvE,OAAO,KAAK,CAAC,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;qBACpD;gBACH,CAAC,CAAC;YACJ,CAAC,CAAC;YACF,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzD,MAAM,UAAU,GAAG,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAE9D,IAAI,YAAY,EAAE;gBAChB,OAAO,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,CAAC,KAAK,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;aAClE;iBAAM,IAAI,CAAC,UAAU,EAAE;gBACtB,OAAO,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aAC1D;iBAAM;gBACL,OAAO;oBACL,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,EAAE;oBAC5D,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;iBACvD,CAAC;aACH;QACH,CAAC;QAED,SAAS,gBAAgB,CAAC,IAAwB;YAChD,MAAM,UAAU,GAAG,CACjB,IAAI,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CACzB,CAAC;YAC9B,OAAQ,UAAU,CAAC,QAA6B,CAAC,KAAK,KAAK,KAAK,CAAC;QACnE,CAAC;QAED,SAAS,mBAAmB,CAAC,IAAyB;YACpD,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,iBAAiB,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAC;gBACrE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,QAAQ,CAChF,IAAI,CAAC,QAAQ,CACd,CACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,iBAAS,IAAI,CAAC"}