{"version": 3, "file": "equivalence.js", "sourceRoot": "", "sources": ["../../src/utils/equivalence.ts"], "names": [], "mappings": ";;;AAqBA;;;;GAIG;AACH,SAAgB,aAAa,CAC3B,KAAsC,EACtC,MAAuC,EACvC,UAA+B;IAE/B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACjD,OAAO,CACL,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM;YAC9B,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC,CAAC,CACvF,CAAC;KACH;SAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC1D,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;YAC1B,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CACzE,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAjBD,sCAiBC;AAED,SAAS,aAAa,CAAC,WAAiC,EAAE,YAAkC;IAC1F,OAAO,CACL,WAAW,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM;QAC1C,WAAW,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,KAAK,YAAY,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CACzF,CAAC;AACJ,CAAC"}