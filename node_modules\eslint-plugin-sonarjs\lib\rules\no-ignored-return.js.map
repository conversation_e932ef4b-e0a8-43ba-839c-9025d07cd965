{"version": 3, "file": "no-ignored-return.js", "sourceRoot": "", "sources": ["../../src/rules/no-ignored-return.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,8DAA4F;AAC5F,gDAAwC;AACxC,oCAA+C;AAE/C,MAAM,4BAA4B,GAAqC;IACrE,KAAK,EAAE,IAAI,GAAG,CAAC;QACb,QAAQ;QACR,UAAU;QACV,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,SAAS;QACT,QAAQ;QACR,WAAW;QACX,MAAM;QACN,KAAK;QACL,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,aAAa;QACb,UAAU;QACV,gBAAgB;KACjB,CAAC;IACF,IAAI,EAAE,IAAI,GAAG,CAAC;QACZ,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,iBAAiB;QACjB,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,SAAS;QACT,mBAAmB;QACnB,YAAY;QACZ,WAAW;QACX,gBAAgB;QAChB,aAAa;QACb,oBAAoB;QACpB,eAAe;QACf,aAAa;QACb,eAAe;QACf,SAAS;QACT,cAAc;QACd,aAAa;QACb,QAAQ;QACR,aAAa;QACb,oBAAoB;QACpB,oBAAoB;QACpB,cAAc;QACd,aAAa;QACb,UAAU;QACV,gBAAgB;KACjB,CAAC;IACF,IAAI,EAAE,IAAI,GAAG,CAAC;QACZ,KAAK;QACL,GAAG;QACH,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,IAAI;QACJ,SAAS;QACT,OAAO;QACP,KAAK;QACL,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,KAAK;QACL,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;QACP,OAAO;QACP,MAAM;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,QAAQ;QACR,OAAO;QACP,MAAM;QACN,KAAK;QACL,MAAM;QACN,MAAM;QACN,KAAK;QACL,MAAM;QACN,OAAO;KACR,CAAC;IACF,MAAM,EAAE,IAAI,GAAG,CAAC,CAAC,eAAe,EAAE,SAAS,EAAE,aAAa,EAAE,gBAAgB,EAAE,UAAU,CAAC,CAAC;IAC1F,MAAM,EAAE,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IACrC,MAAM,EAAE,IAAI,GAAG,CAAC;QACd,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,QAAQ;QACR,UAAU;QACV,UAAU;QACV,SAAS;QACT,aAAa;QACb,eAAe;QACf,OAAO;QACP,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,OAAO;QACP,OAAO;QACP,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,mBAAmB;QACnB,mBAAmB;QACnB,aAAa;QACb,aAAa;QACb,MAAM;QACN,QAAQ;QACR,UAAU;QACV,SAAS;QAET,uBAAuB;QACvB,QAAQ;QACR,KAAK;QACL,OAAO;QACP,MAAM;QACN,OAAO;QACP,WAAW;QACX,UAAU;QACV,SAAS;QACT,MAAM;QACN,OAAO;QACP,QAAQ;QACR,KAAK;QACL,KAAK;KACN,CAAC;CACH,CAAC;AAEF,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,UAAU,EAAE,uFAAuF;YACnG,qBAAqB,EAAE,oDAAoD;SAC5E;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,yEAAyE;YACtF,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAA+C;QACpD,IAAI,CAAC,IAAA,0CAAwB,EAAC,OAAO,CAAC,cAAc,CAAC,EAAE;YACrD,OAAO,EAAE,CAAC;SACX;QACD,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACxC,OAAO;YACL,cAAc,EAAE,CAAC,IAAmB,EAAE,EAAE;gBACtC,MAAM,IAAI,GAAG,IAA+B,CAAC;gBAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;gBACxB,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;oBACtC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;oBACxB,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,qBAAqB,EAAE;wBACnD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;wBACpE,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO;6BAChC,cAAc,EAAE;6BAChB,iBAAiB,CAChB,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,MAAuB,CAAC,CACnE,CAAC;wBACJ,IACE,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;4BAChD,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,EAC5D;4BACA,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;yBACpD;qBACF;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,qBAAqB,CAC5B,UAAkB,EAClB,aAAkE,EAClE,QAAgC;IAEhC,IAAI,UAAU,KAAK,SAAS,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;QACxD,MAAM,IAAI,GAAG,IAAA,2BAAmB,EAAC,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QAC7D,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAC9F,yGAAyG;QACzG,4EAA4E;QAC5E,6DAA6D;QAC7D,MAAM,EAAE,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;QACjC,OAAO,QAAQ,IAAI,EAAE,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;KACpD;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,gBAAgB,CACvB,UAAkB,EAClB,IAAmB;IAEnB,IAAI,UAAU,KAAK,KAAK,EAAE;QACxB,OAAO;YACL,SAAS,EAAE,YAAY;YACvB,IAAI;SACL,CAAC;KACH;SAAM;QACL,OAAO;YACL,SAAS,EAAE,uBAAuB;YAClC,IAAI;YACJ,IAAI,EAAE,EAAE,UAAU,EAAE;SACrB,CAAC;KACH;AACH,CAAC;AAED,SAAS,aAAa,CAAC,UAAkB,EAAE,UAAe,EAAE,QAAgC;IAC1F,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACxD,IAAI,YAAY,KAAK,IAAI,EAAE;QACzB,MAAM,OAAO,GAAG,4BAA4B,CAAC,YAAY,CAAC,CAAC;QAC3D,OAAO,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;KAC9C;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,YAAY,CAAC,EAAO,EAAE,QAAgC;IAC7D,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;IAEtD,MAAM,QAAQ,GAAG,WAAW,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IACxD,IAAI,YAAY,KAAK,QAAQ,IAAI,YAAY,KAAK,QAAQ,EAAE;QAC1D,OAAO,YAAY,CAAC;KACrB;IAED,MAAM,MAAM,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;IAC9B,IAAI,MAAM,EAAE;QACV,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QAC9B,QAAQ,IAAI,EAAE;YACZ,KAAK,OAAO,CAAC;YACb,KAAK,MAAM,CAAC;YACZ,KAAK,MAAM,CAAC;YACZ,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;SAC7B;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,iBAAS,IAAI,CAAC"}