{"version": 3, "file": "non-existent-operator.js", "sourceRoot": "", "sources": ["../../src/rules/non-existent-operator.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,gDAAwC;AAExC,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,mBAAmB,EAAE,oCAAoC;YACzD,uBAAuB,EAAE,sCAAsC;SAChE;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;QACf,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE;YACJ,WAAW,EAAE,+DAA+D;YAC5E,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,oBAAoB,CAAC,IAAmB;gBACtC,MAAM,oBAAoB,GAAG,IAAqC,CAAC;gBACnE,IAAI,oBAAoB,CAAC,QAAQ,KAAK,GAAG,EAAE;oBACzC,aAAa,CAAC,OAAO,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAC;iBACpD;YACH,CAAC;YACD,kBAAkB,CAAC,IAAmB;gBACpC,MAAM,kBAAkB,GAAG,IAAmC,CAAC;gBAC/D,aAAa,CAAC,OAAO,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,aAAa,CACpB,OAA+C,EAC/C,SAAsC;IAEtC,IACE,SAAS;QACT,SAAS,CAAC,IAAI,KAAK,iBAAiB;QACpC,yBAAyB,CAAC,SAAS,CAAC,QAAQ,CAAC,EAC7C;QACA,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,uBAAuB,GAAG,UAAU,CAAC,cAAc,CACvD,SAAS,EACT,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,CAC7B,CAAC;QACF,MAAM,kBAAkB,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAC/D,MAAM,oBAAoB,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE1E,IACE,uBAAuB,IAAI,IAAI;YAC/B,kBAAkB,IAAI,IAAI;YAC1B,oBAAoB,IAAI,IAAI;YAC5B,WAAW,CAAC,uBAAuB,EAAE,kBAAkB,CAAC;YACxD,CAAC,WAAW,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,EACtD;YACA,MAAM,OAAO,GAA2C,EAAE,CAAC;YAC3D,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,KAAK,sBAAsB,EAAE;gBACrD,MAAM,KAAK,GAAqB;oBAC9B,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;oBAChC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC5B,CAAC;gBACF,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,GAAG,uBAAuB,CAAC,KAAK,CAAC;gBACnF,OAAO,CAAC,IAAI,CAAC;oBACX,SAAS,EAAE,yBAAyB;oBACpC,IAAI,EAAE;wBACJ,QAAQ,EAAE,iBAAiB;qBAC5B;oBACD,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,CAAC;iBAC/D,CAAC,CAAC;aACJ;YACD,OAAO,CAAC,MAAM,CAAC;gBACb,SAAS,EAAE,qBAAqB;gBAChC,IAAI,EAAE;oBACJ,QAAQ,EAAE,SAAS,CAAC,QAAQ;iBAC7B;gBACD,GAAG,EAAE,EAAE,KAAK,EAAE,uBAAuB,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAClF,OAAO;aACR,CAAC,CAAC;SACJ;KACF;AACH,CAAC;AAED,SAAS,yBAAyB,CAAC,QAA8C;IAC/E,OAAO,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,CAAC;AAClE,CAAC;AAED,SAAS,WAAW,CAAC,KAAyB,EAAE,MAA0B;IACxE,OAAO,CACL,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CACjG,CAAC;AACJ,CAAC;AAED,iBAAS,IAAI,CAAC"}