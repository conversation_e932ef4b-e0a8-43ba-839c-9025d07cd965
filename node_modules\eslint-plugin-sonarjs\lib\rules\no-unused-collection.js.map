{"version": 3, "file": "no-unused-collection.js", "sourceRoot": "", "sources": ["../../src/rules/no-unused-collection.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,sDAA6E;AAC7E,gDAAwC;AAExC,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,gBAAgB,EAAE,iEAAiE;SACpF;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,8CAA8C;YAC3D,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,cAAc,EAAE,GAAG,EAAE;gBACnB,MAAM,YAAY,GAA8B,EAAE,CAAC;gBACnD,wBAAwB,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,YAAY,CAAC,CAAC;gBAE3D,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;oBACjC,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,kBAAkB;wBAC7B,IAAI,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC;qBACjC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,wBAAwB,CAC/B,KAA2B,EAC3B,WAAsC;IAEtC,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC3B,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACrD,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;KACJ;IAED,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QACrC,wBAAwB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,UAAU,CAAC,QAAiC;IACnD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC,OAAO,UAAU,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AACjF,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAiC;IAC3D,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IACD,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE;QACnC,OAAO,KAAK,CAAC;KACd;IACD,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAE7B,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,UAAU,EAAE;QACrC,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE;YACrB,IAAI,8BAA8B,CAAC,GAAG,CAAC,EAAE;gBACvC,gBAAgB,GAAG,IAAI,CAAC;aACzB;iBAAM;gBACL,yDAAyD;gBACzD,OAAO,KAAK,CAAC;aACd;SACF;aAAM,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE;YACtB,sIAAsI;YACtI,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,SAAS,8BAA8B,CAAC,GAA6B;IACnE,MAAM,cAAc,GAAG,yBAAyB,CAC9C,GAAG,CAAC,UAA2B,EAC/B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAqB,CACxD,CAAC;IACnB,IAAI,cAAc,EAAE;QAClB,IAAI,cAAc,CAAC,IAAI,KAAK,oBAAoB,IAAI,cAAc,CAAC,IAAI,EAAE;YACvE,OAAO,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC9C;QAED,IAAI,cAAc,CAAC,IAAI,KAAK,qBAAqB,EAAE;YACjD,MAAM,EAAE,UAAU,EAAE,GAAG,cAAc,CAAC;YACtC,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,sBAAsB;gBAC1C,aAAa,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;gBACnC,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CACnC,CAAC;SACH;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAmB;IAC3C,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,EAAE;QAC3C,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAgB,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,EAAE;QACpF,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,mCAAqB,CAAC,CAAC;KAC5D;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAC,GAA6B;IAC3C,MAAM,mBAAmB,GAAG,yBAAyB,CACnD,GAAG,CAAC,UAA2B,EAC/B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,qBAAqB,CACN,CAAC;IAElC,IAAI,mBAAmB,EAAE;QACvB,OAAO,CAAC,CACN,cAAc,CAAC,mBAAmB,EAAE,GAAG,CAAC,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAC1F,CAAC;KACH;IAED,uGAAuG;IACvG,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;GAGG;AACH,SAAS,mBAAmB,CAC1B,SAAuC,EACvC,GAA6B;IAE7B,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,KAAK,gBAAgB,EAAE;QAClD,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC,UAAU,CAAC;QACxC,IAAI,kBAAkB,CAAC,MAAM,CAAC,EAAE;YAC9B,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YAC5B,OAAO,aAAa,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,EAAE,GAAG,4BAAc,CAAC,CAAC;SACvF;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAmB;IAC7C,OAAO,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAC;AAC1C,CAAC;AAED;;;;;;GAMG;AACH,SAAS,cAAc,CAAC,SAAuC,EAAE,GAA6B;IAC5F,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAsB,EAAE;QACxD,MAAM,oBAAoB,GAAG,SAAS,CAAC,UAAU,CAAC;QAClD,MAAM,GAAG,GAAG,oBAAoB,CAAC,IAAI,CAAC;QACtC,OAAO,2BAA2B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;KAC9C;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,2BAA2B,CAAC,GAAkB,EAAE,GAA6B;IACpF,OAAO,GAAG,CAAC,IAAI,KAAK,kBAAkB,IAAI,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AAC3E,CAAC;AAED,SAAS,YAAY,CAAC,IAAmB,EAAE,GAAG,MAAgB;IAC5D,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC;AACjF,CAAC;AAED,SAAS,aAAa,CAAC,GAA6B,EAAE,IAAmB;IACvE,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC;AAC/D,CAAC;AAED,SAAS,yBAAyB,CAChC,IAAmB,EACnB,SAA2C;IAE3C,OAAO,cAAc,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,cAAc,CAAC,IAAmB,EAAE,aAA0B;IACrE,MAAM,KAAK,GAAoB,EAAE,CAAC;IAElC,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;IAC9B,OAAO,WAAW,EAAE;QAClB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxB,IAAI,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;YACvC,MAAM;SACP;QACD,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;KAClC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,iBAAS,IAAI,CAAC"}