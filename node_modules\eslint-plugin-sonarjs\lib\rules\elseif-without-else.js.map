{"version": 3, "file": "elseif-without-else.js", "sourceRoot": "", "sources": ["../../src/rules/elseif-without-else.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,mDAAmD;AAGnD,gDAAwC;AAExC,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,oBAAoB,EAAE,gCAAgC;SACvD;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,4DAA4D;YACzE,WAAW,EAAE,KAAK;YAClB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,WAAW,EAAE,CAAC,IAAmB,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,IAA4B,CAAC;gBAC5C,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;oBACzC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;oBAC3C,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,CAC3C,IAAI,EACJ,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,CAC5D,CAAC;oBACF,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CACxC,IAAI,EACJ,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CAC1D,CAAC;oBACF,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,sBAAsB;wBACjC,GAAG,EAAE;4BACH,KAAK,EAAE,WAAY,CAAC,GAAG,CAAC,KAAK;4BAC7B,GAAG,EAAE,SAAU,CAAC,GAAG,CAAC,GAAG;yBACxB;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,QAAQ,CAAC,IAA0B;IAC1C,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IACxB,OAAO,MAAM,EAAE,IAAI,KAAK,aAAa,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC;AACrE,CAAC;AAED,iBAAS,IAAI,CAAC"}