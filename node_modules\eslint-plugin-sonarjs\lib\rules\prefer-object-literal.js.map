{"version": 3, "file": "prefer-object-literal.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-object-literal.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,oDAAoD;AAGpD,0CAQwB;AACxB,sDAAqD;AACrD,gDAAwC;AAExC,MAAM,IAAI,GAA0C;IAClD,IAAI,EAAE;QACJ,QAAQ,EAAE;YACR,6BAA6B,EAC3B,yHAAyH;SAC5H;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,sCAAsC;YACnD,WAAW,EAAE,OAAO;YACpB,GAAG,EAAE,IAAA,kBAAO,EAAC,UAAU,CAAC;SACzB;KACF;IACD,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,cAAc,EAAE,CAAC,IAAmB,EAAE,EAAE,CACtC,yBAAyB,CAAE,IAAgC,CAAC,IAAI,EAAE,OAAO,CAAC;YAC5E,OAAO,EAAE,CAAC,IAAmB,EAAE,EAAE;gBAC/B,MAAM,UAAU,GAAI,IAAyB,CAAC,IAAI,CAAC,MAAM,CACvD,CAAC,SAAS,EAAmC,EAAE,CAAC,CAAC,IAAA,2BAAmB,EAAC,SAAS,CAAC,CAChF,CAAC;gBACF,yBAAyB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACjD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,SAAS,yBAAyB,CAChC,UAAgC,EAChC,OAA+C;IAE/C,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,OAAO,KAAK,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;QACpC,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QAClE,qDAAqD;QACrD,IAAI,iBAAiB,IAAI,IAAA,oBAAY,EAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE;YAC3D,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACvC,IAAI,oBAAoB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,EAAE;gBACjF,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,+BAA+B,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;aACzF;SACF;QACD,KAAK,EAAE,CAAC;KACT;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,SAA6B;IACzD,IAAI,IAAA,6BAAqB,EAAC,SAAS,CAAC,EAAE;QACpC,OAAO,SAAS,CAAC,YAAY,CAAC,IAAI,CAChC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,IAAI,uBAAuB,CAAC,WAAW,CAAC,IAAI,CAAC,CAC/E,CAAC;KACH;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,uBAAuB,CAAC,UAA+B;IAC9D,OAAO,IAAA,0BAAkB,EAAC,UAAU,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,oBAAoB,CAC3B,SAA6B,EAC7B,gBAAqC,EACrC,UAA+B;IAE/B,IAAI,IAAA,6BAAqB,EAAC,SAAS,CAAC,IAAI,IAAA,8BAAsB,EAAC,SAAS,CAAC,UAAU,CAAC,EAAE;QACpF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,CAAC,UAAU,CAAC;QAC7C,IAAI,IAAA,0BAAkB,EAAC,IAAI,CAAC,EAAE;YAC5B,OAAO,CACL,CAAC,IAAI,CAAC,QAAQ;gBACd,sBAAsB,CAAC,KAAK,EAAE,UAAU,CAAC;gBACzC,IAAA,2BAAa,EAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,EAAE,UAAU,CAAC;gBACxD,CAAC,mBAAmB,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAC9C,CAAC;SACH;KACF;IACD,OAAO,KAAK,CAAC;IAEb,SAAS,sBAAsB,CAC7B,UAA+B,EAC/B,UAA+B;QAE/B,MAAM,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,UAAU,CAAE,CAAC,GAAG,CAAC;QACxD,MAAM,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,UAAU,CAAE,CAAC,GAAG,CAAC;QACtD,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IAC5C,CAAC;IAED,SAAS,mBAAmB,CAC1B,IAA+B,EAC/B,KAA0B,EAC1B,UAA+B;QAE/B,OAAO,IAAA,2BAAa,EAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IACvD,CAAC;AACH,CAAC;AAED,iBAAS,IAAI,CAAC"}